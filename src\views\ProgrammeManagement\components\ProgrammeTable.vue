<script setup lang="tsx">
  import { useI18n } from '@/hooks/web/useI18n';
  import { Search } from '@/components/Search';
  import { Table } from '@/components/Table';
  import type { BatchOption } from '@/components/Table/src/types';
  import { useTable } from '@/hooks/web/useTable';
  import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas';
  import { ref, reactive, unref, toRef, onMounted, watch, nextTick } from 'vue';
  import ButtonGroup from '@/components/ButtonGroup/src/ButtonGroup.vue';
  import type { ButtonGroupSchema } from '@/components/ButtonGroup/src/types';
  import {
    getProgrammeListApi,
    removeProgrammeApi,
    saveProgrammeApi,
    startComparingApi,
    circleComparingApi,
    updateProgrammeApi
  } from '@/api/programmeManagement';
  import type { IQueryData, IProgrammeTable } from '@/api/programmeManagement/types';
  import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ElPopconfirm } from 'element-plus';
  import AddProgramme from './AddProgramme.vue';
  import CycleComparison from './CycleComparison.vue';
  import config from '@/config/axios/config';
  import { useValidator } from '@/hooks/web/useValidator';
  const { t } = useI18n();
  const { result_code } = config;
  const { required } = useValidator();

  const crudSchemas = reactive<CrudSchema[]>([
    {
      field: 'selection', //复选框
      table: {
        type: 'selection'
      }
    },
    {
      field: 'index',
      label: t('demo.index'),
      type: 'index'
    },
    {
      field: 'name',
      label: t('一致性比对.方案名称'),
      minWidth: 120,
      search: {
        component: 'Input'
      },
      form: {
        component: 'Input',
        formItemProps: {
          rules: [required(t('场景管理.请输入方案名称'))]
        },
        componentProps: {
          placeholder: t('一致性比对.请输入方案名称')
        },
        colProps: {
          span: 24
        }
      }
    },
    {
      field: 'planDesc',
      label: t('一致性比对.方案描述'),
      minWidth: 120,
      form: {
        component: 'Input',
        formItemProps: {
          rules: [required(t('场景管理.请输入方案名称'))]
        },
        componentProps: {
          type: 'textarea',
          placeholder: t('一致性比对.请输入方案描述'),
          autosize: {
            minRows: 4
          }
        },
        colProps: {
          span: 24
        }
      }
    },
    {
      field: 'createTime',
      label: t('一致性比对.创建时间'),
      width: 170
    },
    {
      field: 'creatorName',
      label: t('一致性比对.创建人')
    },
    {
      field: 'action',
      width: 120,
      label: t('common.handle'),
      table: {
        fixed: 'right',
        slots: {
          default: (data: any) => {
            return (
              <div>
                <ElButton
                  link
                  type="primary"
                  onClick={() => onActionFn(data.row)}
                  v-hasPerm="updatePlan"
                >
                  {t('common.edit')}
                </ElButton>
                <ElDivider direction="vertical" v-hasPerm="updatePlan" />
                <ElPopconfirm title={t('common.delMessage')} onConfirm={() => delData(data.row)}>
                  {{
                    reference: () => {
                      return (
                        <ElButton link type="danger" v-hasPerm="removePlan">
                          {t('common.delete')}
                        </ElButton>
                      );
                    }
                  }}
                </ElPopconfirm>
              </div>
            );
          }
        }
      }
    }
  ]);
  const { allSchemas } = useCrudSchemas(crudSchemas);
  //列表定义
  const queryData = reactive<IQueryData>({
    queryParam: {},
    pageNum: 1,
    pageSize: 10
  });
  const searchParams = toRef(queryData, 'queryParam');
  const selectedItemIds = ref<number[]>([]);
  const { tableRegister, tableState, tableMethods } = useTable({
    fetchDataApi: async () => {
      const { currentPage, pageSize } = tableState;
      queryData.pageNum = unref(currentPage);
      queryData.pageSize = unref(pageSize);
      const res = await getProgrammeListApi(queryData);
      nextTick(() => {
        if (res.data.list.length > 0) {
          rowClickFn(res.data.list[0]);
          setCurrent(res.data.list[0]);
        } else {
          currentRowInfo.value = {};
          setCurrent();
        }
      });
      return {
        list: res.data.list,
        total: res.data.total
      };
    },
    fetchDelApi: async () => {
      const res = await removeProgrammeApi(selectedItemIds.value);
      return !!res;
    }
  });
  const { loading, dataList, total, currentPage, pageSize } = tableState;
  const { getList, getElTableExpose, refresh, delList } = tableMethods;

  let elTableExpose = ref<ComponentRef<typeof ElTable>>();
  let addprogrammeRef = ref<ComponentRef<typeof AddProgramme>>();
  let cyclecomparisonRef = ref<ComponentRef<typeof CycleComparison>>();
  const pageLoading = ref(false);
  const loadingText = ref('比对中...');
  const batchCount = ref<number>(0);
  const currentRowInfo = ref<any>({});
  const actionVisible = ref(false);
  const actionTitle = ref('');
  const actionType = ref('add');
  const saveLoading = ref(false);
  const cycleVisible = ref(false);
  const saveCycleVisibleLoading = ref(false);

  /*按钮组定义*/
  const buttonSchema = ref<ButtonGroupSchema[]>([
    {
      type: 'primary',
      field: 'add',
      name: t('common.add'),
      perm: 'savePlan',
      option: () => {
        onActionFn(null);
      }
    },
    {
      type: 'primary',
      field: 'cyclecomparison',
      name: t('一致性比对.周期比对'),
      perm: 'saveScheduleCompare',
      option: () => {
        cycleComparisonFn();
      }
    },
    {
      type: 'primary',
      field: 'comparing',
      name: t('一致性比对.开始比对'),
      perm: 'startImmePlanCompare',
      option: () => {
        startComparingFn();
      }
    }
  ]);
  /*批处理表格*/
  const batchOptions = ref<BatchOption[]>([
    {
      name: t('common.delete'),
      icon: 'mdi:trash-can-outline',
      title: t('common.delMessage'),
      option: () => {
        delData(null);
      }
    }
  ]);

  //查询
  const setSearchParams = (params: any) => {
    searchParams.value = params;
    getList();
  };

  //删除
  const delLoading = ref(false);
  const delData = async (row: IProgrammeTable | null) => {
    selectedItemIds.value = row
      ? [row.id]
      : elTableExpose.value!.getSelectionRows().map((v: IProgrammeTable) => v.id) || [];
    delLoading.value = true;
    await delList(unref(selectedItemIds).length).finally(() => {
      delLoading.value = false;
    });
  };

  const emit = defineEmits(['rowClick']);
  const rowClickFn = (row: any) => {
    // currentRowInfo.value = row;
    emit('rowClick', row);
  };

  const setCurrent = (row?: any) => {
    elTableExpose.value!.setCurrentRow(row);
  };

  //新增
  const onActionFn = (row: any) => {
    currentRowInfo.value = row || {};
    actionType.value = row ? 'edit' : 'add';
    actionTitle.value = row
      ? t('common.edit') + t('一致性比对.方案')
      : t('common.add') + t('一致性比对.方案');
    actionVisible.value = true;
  };

  // 保存方案
  const saveFn = async () => {
    const formData = await addprogrammeRef.value?.submit();
    if (formData) {
      saveLoading.value = true;
      const saveFunc =
        actionType.value === 'add'
          ? (data: any) => saveProgrammeApi(data)
          : (data: any) => updateProgrammeApi(data);
      const res: any = await saveFunc(formData)
        .catch(() => {})
        .finally(() => {
          saveLoading.value = false;
        });
      if (res?.code === result_code) {
        actionType.value === 'add'
          ? ElMessage.success(t('common.addSuccess'))
          : ElMessage.success(t('common.editSuccess'));
        actionVisible.value = false;
        getList();
      } else {
        ElMessage.error(res?.message);
      }
    }
  };

  // 开始比对
  const startComparingFn = async () => {
    const selectedRows = elTableExpose.value?.getSelectionRows();
    if (selectedRows?.length == 0) {
      ElMessage.warning(t('一致性比对.请选择方案'));
      return;
    }
    pageLoading.value = true;
    const params = {
      planIds: selectedRows.map((v: IProgrammeTable) => v.id)
    };
    const res = await startComparingApi(params)
      .catch(() => {})
      .finally(() => {
        pageLoading.value = false;
      });
    if (res?.code === result_code) {
      ElMessage.success(t('一致性比对.比对成功'));
      elTableExpose.value!.clearSelection();
    } else {
      ElMessage.error(res?.message);
    }
  };

  // 周期比对
  const cycleComparisonFn = () => {
    const selectedRows = elTableExpose.value?.getSelectionRows();
    if (selectedRows?.length == 0) {
      ElMessage.warning(t('一致性比对.请选择一条方案'));
      return;
    }
    if (selectedRows?.length > 1) {
      ElMessage.warning(t('一致性比对.只能勾选一条方案'));
      return;
    }
    selectedItemIds.value =
      elTableExpose.value!.getSelectionRows().map((v: IProgrammeTable) => v.id) || [];
    cycleVisible.value = true;
  };

  // 保存周期比对
  const saveCycleVisibleFn = async () => {
    await cyclecomparisonRef.value!.submit();
  };

  const submitCycleFn = async (submitDatas: any) => {
    saveCycleVisibleLoading.value = true;
    let params = {
      ...submitDatas,
      envcPlanId: selectedItemIds.value[0]
    };
    const res: any = await circleComparingApi(params).finally(() => {
      saveCycleVisibleLoading.value = false;
    });
    if (res?.code === result_code) {
      ElMessage.success(t('common.success'));
      elTableExpose.value!.clearSelection();
      cycleVisible.value = false;
    } else {
      ElMessage.error(res?.message);
    }
  };

  onMounted(async () => {
    elTableExpose.value = await getElTableExpose();
    watch(
      () => elTableExpose.value?.getSelectionRows(),
      (val) => {
        batchCount.value = val.length;
      },
      { deep: true }
    );
  });
</script>

<template>
  <div v-loading="pageLoading" :element-loading-text="loadingText">
    <Search :schema="allSchemas.searchSchema" @search="setSearchParams" @reset="setSearchParams" />
    <ButtonGroup :schema="buttonSchema" />
    <Table
      class="Rd-table-cursor mt-1"
      v-model:pageSize="pageSize"
      v-model:currentPage="currentPage"
      :columns="allSchemas.tableColumns"
      :data="dataList"
      :loading="loading"
      :pagination="{ total: total }"
      @register="tableRegister"
      @refresh="refresh"
      @row-click="rowClickFn"
      highlight-current-row
      showAction
      :batch-count="batchCount"
      :batch-options="batchOptions"
      :reserve-selection="true"
      row-key="id"
      :active-u-i-d="'ProgrammeManagementProgammeTable'"
      :emptyText="t('common.暂无数据')"
    />
  </div>
  <!-- 新增方案 -->
  <ElDrawer v-model="actionVisible" :title="actionTitle" destroy-on-close size="40%">
    <AddProgramme
      ref="addprogrammeRef"
      :form-schema="allSchemas.formSchema"
      :current-row="currentRowInfo"
    />
    <template #footer>
      <ElButton type="primary" :loading="saveLoading" @click="saveFn">
        {{ t('common.ok') }}
      </ElButton>
      <ElButton @click="actionVisible = false">{{ t('common.cancel') }}</ElButton>
    </template>
  </ElDrawer>
  <!-- 周期比对 -->
  <ElDrawer v-model="cycleVisible" :title="t('一致性比对.周期比对')" destroy-on-close size="40%">
    <CycleComparison
      ref="cyclecomparisonRef"
      :selected-ids="selectedItemIds"
      @submit-cycle="submitCycleFn"
    />
    <template #footer>
      <ElButton type="primary" :loading="saveCycleVisibleLoading" @click="saveCycleVisibleFn">
        {{ t('common.ok') }}
      </ElButton>
      <ElButton @click="cycleVisible = false">{{ t('common.cancel') }}</ElButton>
    </template>
  </ElDrawer>
</template>
<style lang="less">
  .Rd-table-cursor {
    .el-table__body {
      .el-table__row {
        cursor: pointer;
      }
    }
  }
</style>
