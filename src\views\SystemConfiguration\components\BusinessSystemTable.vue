<script setup lang="tsx">
  import { useI18n } from '@/hooks/web/useI18n';
  import { Search } from '@/components/Search';
  import { Table } from '@/components/Table';
  import { useTable } from '@/hooks/web/useTable';
  import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas';
  import { ref, reactive, unref, toRef, watch, onMounted, nextTick } from 'vue';
  import type { BatchOption } from '@/components/Table/src/types';
  import { ElTable, ElButton, ElDrawer, ElMessage } from 'element-plus';
  import {
    getBusinessSystemlistApi,
    addSystemApi,
    removeSystemApi
  } from '@/api/systemConfiguration';
  import ButtonGroup from '@/components/ButtonGroup/src/ButtonGroup.vue';
  import type { ButtonGroupSchema } from '@/components/ButtonGroup/src/types';
  import type { IQueryData, ISystemConfiguration } from '@/api/systemConfiguration/types';
  import config from '@/config/axios/config';
  import CheckSystem from './CheckSystem.vue';

  const { result_code } = config;
  const { t } = useI18n();

  const crudSchemas = reactive<CrudSchema[]>([
    {
      field: 'selection', //复选框
      table: {
        type: 'selection'
      }
    },
    {
      field: 'index',
      label: t('demo.index'),
      type: 'index',
      detail: {
        hidden: true
      }
    },
    {
      field: 'businessSystemName',
      label: t('一致性比对.业务系统'),
      minWidth: 120,
      search: {
        component: 'Input'
      }
    },
    {
      field: 'businessSystemDesc',
      label: t('一致性比对.系统描述'),
      minWidth: 100
    },
    {
      field: 'businessSystemCode',
      label: t('一致性比对.系统缩写'),
      minWidth: 100
    },
    {
      field: 'createTime',
      label: t('一致性比对.创建时间'),
      width: 170
    },
    {
      field: 'creatorName',
      label: t('一致性比对.创建人'),
      minWidth: 100
    }
  ]);
  const { allSchemas } = useCrudSchemas(crudSchemas);
  //列表定义
  const selectedItemIds = ref<number[]>([]);
  const queryData = reactive<IQueryData>({
    queryParam: {},
    pageNum: 1,
    pageSize: 10
  });
  const searchParams = toRef(queryData, 'queryParam');
  const { tableRegister, tableState, tableMethods } = useTable({
    fetchDataApi: async () => {
      const { currentPage, pageSize } = tableState;
      queryData.pageNum = unref(currentPage);
      queryData.pageSize = unref(pageSize);
      const res = await getBusinessSystemlistApi(queryData);
      nextTick(() => {
        if (res.data.list.length > 0) {
          rowClickFn(res.data.list[0]);
          setCurrent(res.data.list[0]);
        } else {
          currentRow.value = {};
          setCurrent();
        }
      });
      return {
        list: res.data.list,
        total: res.data.total
      };
    },
    fetchDelApi: async () => {
      const res = await removeSystemApi(selectedItemIds.value);
      return !!res;
    }
  });
  const { loading, dataList, total, currentPage, pageSize } = tableState;
  const { getList, getElTableExpose, refresh, delList } = tableMethods;

  const currentRow = ref<any>({});
  const actionVisible = ref(false);
  const batchCount = ref<number>(0);
  const saveLoading = ref(false);
  const checksystemRef = ref<InstanceType<typeof CheckSystem>>();

  //删除
  const delLoading = ref(false);
  let elTableExpose = ref<ComponentRef<typeof ElTable>>();
  const delData = async (row: ISystemConfiguration | null) => {
    selectedItemIds.value = row
      ? [row.id]
      : elTableExpose.value!.getSelectionRows().map((v: ISystemConfiguration) => v.id) || [];
    delLoading.value = true;
    await delList(unref(selectedItemIds).length).finally(() => {
      delLoading.value = false;
    });
  };

  //查询
  const setSearchParams = (params: any) => {
    searchParams.value = params;
    getList();
  };
  /*按钮组定义*/
  const buttonSchema = ref<ButtonGroupSchema[]>([
    {
      type: 'primary',
      field: 'add',
      name: t('common.add'),
      perm: 'saveSystem',
      option: () => {
        addActionFn();
      }
    }
  ]);
  /*批处理表格*/
  const batchOptions = ref<BatchOption[]>([
    {
      name: t('common.delete'),
      icon: 'mdi:trash-can-outline',
      title: t('common.delMessage'),
      perm: 'removeSystem',
      option: () => {
        delData(null);
      }
    }
  ]);

  const emit = defineEmits(['rowClick']); // 定义一个自定义事件，用于传递参数给父组件
  const rowClickFn = (row: ISystemConfiguration) => {
    currentRow.value = row;
    emit('rowClick', row); // 触发父组件的事件并传递参数
  };

  const setCurrent = (row?: any) => {
    elTableExpose.value!.setCurrentRow(row);
  };

  // 新增业务系统
  const addActionFn = () => {
    actionVisible.value = true;
  };

  /*保存表单*/
  const saveFn = async () => {
    checksystemRef.value!.submit(); // 调用子组件的方法
  };

  const submitCheckSystem = async (checkDatas: any) => {
    console.log('submitCheckSystem', checkDatas);
    const systemIds = checkDatas.map((v: ISystemConfiguration) => v.businessSystemId) || [];
    saveLoading.value = true;
    const res: any = await addSystemApi(systemIds)
      .catch(() => {})
      .finally(() => {
        saveLoading.value = false;
      });
    if (res && res.code == result_code) {
      ElMessage.success(t('common.addSuccess'));
      actionVisible.value = false;
      currentPage.value = 1;
      await getList();
    } else {
      ElMessage.error(res?.message);
    }
  };

  onMounted(async () => {
    elTableExpose.value = await getElTableExpose();
    watch(
      () => elTableExpose.value?.getSelectionRows(),
      (val) => {
        batchCount.value = val.length;
      },
      { deep: true }
    );
  });
</script>

<template>
  <Search :schema="allSchemas.searchSchema" @search="setSearchParams" @reset="setSearchParams" />
  <ButtonGroup :schema="buttonSchema" />
  <Table
    class="mt-1 Rd-system-table"
    v-model:pageSize="pageSize"
    v-model:currentPage="currentPage"
    :columns="allSchemas.tableColumns"
    :data="dataList"
    :loading="loading"
    :pagination="{ total: total }"
    @register="tableRegister"
    @refresh="refresh"
    @row-click="rowClickFn"
    highlight-current-row
    showAction
    :batch-count="batchCount"
    :batch-options="batchOptions"
    :reserve-selection="true"
    row-key="businessSystemId"
    :active-u-i-d="'SystemConfigurationSystemTable'"
    :emptyText="t('common.暂无数据')"
  />
  <ElDrawer
    v-model="actionVisible"
    :title="t('common.add') + t('一致性比对.业务系统')"
    destroy-on-close
    size="50%"
    class="customForm"
  >
    <CheckSystem ref="checksystemRef" @check-system="submitCheckSystem" />
    <template #footer>
      <ElButton type="primary" :loading="saveLoading" @click="saveFn">
        {{ t('common.ok') }}
      </ElButton>
      <ElButton @click="actionVisible = false">{{ t('common.cancel') }}</ElButton>
    </template>
  </ElDrawer>
</template>
<style lang="less">
  .Rd-system-table {
    .el-table__body {
      .el-table__row {
        cursor: pointer;
      }
    }
  }
</style>
