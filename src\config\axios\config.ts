import type {
  AxiosConfig,
  AxiosResponse,
  AxiosRequestHeaders,
  AxiosError,
  InternalAxiosRequestConfig
} from './types';
import { ElMessage } from 'element-plus';
import qs from 'qs';

const config: AxiosConfig = {
  /**
   * 接口成功返回状态码
   */
  result_code: '10000',

  /**
   * 接口请求超时时间
   */
  timeout: 60000,

  /**
   * 默认接口请求类型
   * 可选值：application/x-www-form-urlencoded multipart/form-data
   */
  defaultHeaders: 'application/json',

  interceptors: {
    //请求拦截
    // requestInterceptors: (config) => {
    //   return config
    // },
    // 响应拦截器
    // responseInterceptors: (result: AxiosResponse) => {
    //   return result
    // }
  }
};

const defaultRequestInterceptors = (config: InternalAxiosRequestConfig) => {
  if (
    config.method === 'post' &&
    (config.headers as AxiosRequestHeaders)['Content-Type'] === 'application/x-www-form-urlencoded'
  ) {
    config.data = qs.stringify(config.data);
  }
  if (config.method === 'get' && config.params) {
    let url = config.url as string;
    url += '?';
    const keys = Object.keys(config.params);
    for (const key of keys) {
      if (config.params[key] !== void 0 && config.params[key] !== null) {
        url += `${key}=${encodeURIComponent(config.params[key])}&`;
      }
    }
    url = url.substring(0, url.length - 1);
    config.params = {};
    config.url = url;
  }
  return config;
};
(error: AxiosError) => {
  console.log(error);
  Promise.reject(error);
};

const defaultResponseInterceptors = (response: AxiosResponse<any>) => {
  if (response?.config?.responseType === 'blob') {
    return new Promise((resolve, reject) => {
      const fileReader = new FileReader(); // 读取文件流
      fileReader.readAsText(response?.data, 'utf-8'); // 开始读取 Blob 内容，返回的result属性是一个字符串
      fileReader.addEventListener('loadend', () => {
        let result: any = null;
        try {
          result = JSON.parse(fileReader?.result as string); // try包裹，防止异常，比如 csv
        } catch {}
        if (result && result.code && result.code !== config.result_code) {
          ElMessage.error(result.message);
          reject(result);
        } else {
          resolve(response); // 返回文件流
        }
      });
    });
  } else if (response.data.code === config.result_code) {
    return response.data;
  } else {
    ElMessage.error({
      dangerouslyUseHTMLString: true,
      message: `<pre>${response.data.message}</pre>`
    });
  }
};
(error: AxiosError) => {
  console.log('err' + error); // for debug
  ElMessage.error(error.message);
  return Promise.reject(error);
};

export { defaultResponseInterceptors, defaultRequestInterceptors };
export default config;
