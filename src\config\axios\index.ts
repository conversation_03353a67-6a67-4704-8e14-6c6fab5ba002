import service from './service';
import config from './config';
import { useCache } from '@/hooks/web/useCache';
const { wsCache } = useCache();

const { defaultHeaders } = config;

const request = (option: AxiosConfig) => {
  const { url, method, params, data, headersType, responseType, timeout } = option;
  return service.request({
    url: url,
    method,
    params,
    data,
    responseType: responseType,
    headers: {
      'Content-Type': (headersType || defaultHeaders) + '; charset=utf-8',
      Authentication: wsCache.get('token')
    },
    timeout: timeout || config.timeout
  });
};

export default {
  get: <T = any>(option: AxiosConfig) => {
    return request({ method: 'get', ...option }) as Promise<IResponse<T>>;
  },
  post: <T = any>(option: AxiosConfig) => {
    return request({ method: 'post', ...option }) as Promise<IResponse<T>>;
  },
  delete: <T = any>(option: AxiosConfig) => {
    return request({ method: 'delete', ...option }) as Promise<IResponse<T>>;
  },
  put: <T = any>(option: AxiosConfig) => {
    return request({ method: 'put', ...option }) as Promise<IResponse<T>>;
  },
  cancelRequest: (url: string | string[]) => {
    return service.cancelRequest(url);
  },
  cancelAllRequest: () => {
    return service.cancelAllRequest();
  }
};
