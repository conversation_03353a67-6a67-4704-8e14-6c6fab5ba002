<script setup lang="tsx">
  import { useI18n } from '@/hooks/web/useI18n';
  import { Table } from '@/components/Table';
  import { useTable } from '@/hooks/web/useTable';
  import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas';
  import { ref, reactive, unref, toRef, onMounted, watch } from 'vue';
  import { rulesComparingApi } from '@/api/programmeManagement';
  import { getRelationRuleListApi } from '@/api/programmeManagement';
  import type { IQueryData } from '@/api/programmeManagement/types';
  import { ElTable, ElMessage, ElLoading } from 'element-plus';
  import ButtonGroup from '@/components/ButtonGroup/src/ButtonGroup.vue';
  import type { ButtonGroupSchema } from '@/components/ButtonGroup/src/types';
  import config from '@/config/axios/config';
  const { result_code } = config;
  const { t } = useI18n();

  const props = defineProps({
    currentRowEquipemnt: {
      type: Object,
      default: () => {}
    }
  });

  const crudSchemas = reactive<CrudSchema[]>([
    {
      field: 'selection', //复选框
      table: {
        type: 'selection'
      }
    },
    {
      field: 'index',
      label: t('demo.index'),
      type: 'index'
    },
    {
      field: 'model',
      label: t('一致性比对.模式'),
      table: {
        slots: {
          default: (data: any) => {
            return (
              <span>
                {data.row.model === 0
                  ? t('一致性比对.比对')
                  : data.row.model === 1
                  ? t('一致性比对.同步')
                  : t('一致性比对.比对后同步')}
              </span>
            );
          }
        }
      }
    },
    {
      field: 'type',
      label: t('一致性比对.类型'),
      table: {
        slots: {
          default: (data: any) => {
            return (
              <span>
                {data.row.type === 0
                  ? t('一致性比对.目录')
                  : data.row.type === 1
                  ? t('一致性比对.文件')
                  : t('一致性比对.脚本')}
              </span>
            );
          }
        }
      }
    },
    {
      field: 'way',
      label: t('一致性比对.方式'),
      table: {
        slots: {
          default: (data: any) => {
            return (
              <span>
                {data.row.way === 0
                  ? t('一致性比对.全部')
                  : data.row.way === 1
                  ? t('一致性比对.部分')
                  : ''}
              </span>
            );
          }
        }
      }
    },
    {
      field: 'sourcePath',
      label: t('一致性比对.源路径')
    },
    {
      field: 'path',
      label: t('一致性比对.目标路径')
    },
    {
      field: 'ruleType',
      label: t('一致性比对.规则类型'),
      table: {
        slots: {
          default: (data: any) => {
            return (
              <span>
                {data.row.ruleType === 0
                  ? t('一致性比对.匹配')
                  : data.row.ruleType === 1
                  ? t('一致性比对.排除')
                  : ''}
              </span>
            );
          }
        }
      }
    }
  ]);
  const { allSchemas } = useCrudSchemas(crudSchemas);
  //列表定义
  const queryData = reactive<IQueryData>({
    queryParam: { envcSystemComputerNodeId: props.currentRowEquipemnt.id },
    pageNum: 1,
    pageSize: 10
  });
  const searchParams = toRef(queryData, 'queryParam');
  const { tableRegister, tableState, tableMethods } = useTable({
    immediate: false,
    fetchDataApi: async () => {
      const { currentPage, pageSize } = tableState;
      queryData.pageNum = unref(currentPage);
      queryData.pageSize = unref(pageSize);
      const res = await getRelationRuleListApi(queryData);
      return {
        list: res.data.list,
        total: res.data.total
      };
    }
  });
  const { loading, dataList, total, currentPage, pageSize } = tableState;
  const { getList, getElTableExpose, refresh } = tableMethods;

  /*按钮组定义*/
  const buttonSchema = ref<ButtonGroupSchema[]>([
    {
      type: 'primary',
      field: 'comparing',
      name: t('一致性比对.开始比对'),
      perm: 'startImmeRuleCompare',
      option: () => {
        startComparingFn();
      }
    }
  ]);

  let elTableExpose = ref<ComponentRef<typeof ElTable>>();
  const batchCount = ref<number>(0);

  const startComparingFn = async () => {
    const selectedRows = elTableExpose.value?.getSelectionRows();
    if (selectedRows?.length == 0) {
      ElMessage.warning(t('一致性比对.请选择规则'));
      return;
    }
    const pageLoading = ElLoading.service({
      lock: true,
      text: '比对中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    const params = {
      ruleIds: selectedRows.map((item: any) => item.id)
    };
    const res = await rulesComparingApi(params)
      .catch(() => {})
      .finally(() => {
        pageLoading.close();
      });
    if (res?.code === result_code) {
      ElMessage.success(t('一致性比对.比对成功'));
      elTableExpose.value?.clearSelection();
    } else {
      ElMessage.error(res?.message);
    }
  };

  onMounted(async () => {
    elTableExpose.value = await getElTableExpose();
    watch(
      () => elTableExpose.value?.getSelectionRows(),
      (val) => {
        batchCount.value = val.length;
      },
      { deep: true }
    );
  });

  watch(
    () => props.currentRowEquipemnt,
    (val) => {
      if (val?.id) {
        searchParams.value = {
          envcSystemComputerNodeId: val.id
        };
        getList();
      }
    },
    { deep: true, immediate: true }
  );
</script>

<template>
  <ButtonGroup :schema="buttonSchema" />
  <Table
    class="mt-1"
    v-model:pageSize="pageSize"
    v-model:currentPage="currentPage"
    :columns="allSchemas.tableColumns"
    :data="dataList"
    :loading="loading"
    :pagination="{ total: total }"
    @register="tableRegister"
    @refresh="refresh"
    :batch-count="batchCount"
    :reserve-selection="true"
    showAction
    row-key="id"
    :active-u-i-d="'ProgrammeManagementModuleTable'"
    :emptyText="t('common.暂无数据')"
  />
</template>
