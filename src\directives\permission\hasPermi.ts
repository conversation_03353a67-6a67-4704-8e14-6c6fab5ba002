import type { App, Directive, DirectiveBinding } from 'vue';
import { useI18n } from '@/hooks/web/useI18n';

const { t } = useI18n();

const hasPermission = (value: string): boolean => {
  const permission = ((window as any).$wujie?.props?.meta?.permission || []) as string[];
  if (!value) {
    throw new Error(t('permission.hasPermission'));
  }
  if (permission.includes(value)) {
    return true;
  }
  return false;
};
function hasPerm(el: Element, binding: DirectiveBinding) {
  const value = binding.value;

  const flag = hasPermission(value);
  if (!flag) {
    el.parentNode?.removeChild(el);
  }
}
const mounted = (el: Element, binding: DirectiveBinding<any>) => {
  hasPerm(el, binding);
};

const permiDirective: Directive = {
  mounted
};

export const setupPermissionDirective = (app: App<Element>) => {
  app.directive('hasPerm', permiDirective);
};

export const hasPermi = (value: string) => {
  return hasPermission(value);
};

export default permiDirective;
