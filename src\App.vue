<script setup lang="ts">
  import { computed, nextTick, ref } from 'vue';
  import { useAppStore } from './store/modules/app';
  import { RouterView, useRouter } from 'vue-router';
  import { ConfigGlobal } from '@/components/ConfigGlobal';

  const appStore = useAppStore();

  const currentSize = computed(() => appStore.getCurrentSize);
  const router = useRouter();
  const router_alive = ref(true);
  const wujiePath = (window as any)?.$wujie?.props?.meta?.wujiePath;
  if (wujiePath && router.currentRoute.value.path !== wujiePath) {
    console.log('路由不一致', router.currentRoute.value.path, wujiePath);
    router.replace(wujiePath);
  }
  (window as any).$wujie?.bus.$on(`${import.meta.env.VITE_PROJECT_NAME}-router-change`, (path) => {
    if (router.currentRoute.value.path !== path) {
      router.push(path);
    } else {
      router_alive.value = false;
      nextTick(() => {
        router_alive.value = true;
      });
    }
  });
</script>

<template>
  <ConfigGlobal :size="currentSize">
    <RouterView v-if="router_alive" />
  </ConfigGlobal>
</template>
