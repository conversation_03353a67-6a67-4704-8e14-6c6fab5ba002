//查询
export interface IQueryData {
  queryParam: any;
  pageNum: number;
  pageSize: number;
}
export interface ISystemConfiguration {
  id: string;
  businessSystemId: string;
  businessSystemName: string;
  businessSystemDesc: string;
  creatorName: string;
  createTime: string;
}

export interface IServerTable {
  id: string | number;
  computerName: string;
  computerIp: string;
  centerName: string;
}

export interface IRuleTable {
  id: string | number;
  mode: string;
  type: string;
  character: string;
  sourcepath: string;
  portpath: string;
  status: number;
}

export interface IRuleForm {
  model: string | number;
  type: string | number;
  encode: string;
  sourcePath: string;
  path: string;
  way: number;
  childLevel?: number;
  ruleType?: number;
  content?: string;
}
