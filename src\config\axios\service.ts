import axios, { AxiosError } from 'axios';
import config, { defaultRequestInterceptors, defaultResponseInterceptors } from './config';

import type {
  AxiosInstance,
  InternalAxiosRequestConfig,
  RequestConfig,
  AxiosResponse
} from './types';
import { ElMessage, ElMessageBox } from 'element-plus';
import JSONbig from 'json-bigint';
import { useI18n } from '@/hooks/web/useI18n';

const { t } = useI18n();

const { interceptors } = config;

const { requestInterceptors, responseInterceptors } = interceptors;

const abortControllerMap: Map<string, AbortController> = new Map();

const axiosInstance: AxiosInstance = axios.create({
  ...config,
  baseURL: import.meta.env.VITE_API_BASE_PATH,
  transformResponse: [
    (data) => {
      try {
        return JSONbig({ storeAsString: true }).parse(data);
      } catch {
        return data;
      }
    }
  ]
});

axiosInstance.interceptors.request.use((res: InternalAxiosRequestConfig) => {
  const controller = new AbortController();
  const url = res.url || '';
  res.signal = controller.signal;
  abortControllerMap.set(url, controller);
  return res;
});

axiosInstance.interceptors.response.use(
  (res: AxiosResponse) => {
    const url = res.config.url || '';
    abortControllerMap.delete(url);
    // 这里不能做任何处理，否则后面的 interceptors 拿不到完整的上下文了
    return res;
  },
  (error: AxiosError) => {
    console.log('err: ' + error); // for debug
    // 在这里处理 401 错误，告诉主应用处理
    if (error.response?.status === 401) {
      // @ts-ignore
      ElMessageBox.alert(error.response?.data?.message, t('common.reminder'), {
        confirmButtonText: t('common.ok'),
        type: 'error'
      })
        .then(() => {
          console.log('Unauthorized');
          // @ts-ignore
          window.$wujie?.bus.$emit('Unauthorized');
        })
        .catch((e) => {
          console.log(e);
        });
    } else {
      ElMessage.error(error.message);
    }
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.request.use(requestInterceptors || defaultRequestInterceptors);
axiosInstance.interceptors.response.use(responseInterceptors || defaultResponseInterceptors);

const service = {
  request: (config: RequestConfig) => {
    return new Promise((resolve, reject) => {
      if (config.interceptors?.requestInterceptors) {
        config = config.interceptors.requestInterceptors(config as any);
      }

      axiosInstance
        .request(config)
        .then((res) => {
          resolve(res);
        })
        .catch((err: any) => {
          reject(err);
        });
    });
  },
  cancelRequest: (url: string | string[]) => {
    const urlList = Array.isArray(url) ? url : [url];
    for (const _url of urlList) {
      abortControllerMap.get(_url)?.abort();
      abortControllerMap.delete(_url);
    }
  },
  cancelAllRequest() {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars,no-unused-vars
    for (const [_, controller] of abortControllerMap) {
      controller.abort();
    }
    abortControllerMap.clear();
  }
};

export default service;
