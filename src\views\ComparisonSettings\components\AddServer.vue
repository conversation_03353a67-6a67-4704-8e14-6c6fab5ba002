<script setup lang="tsx">
  import { useI18n } from '@/hooks/web/useI18n';
  import { Table } from '@/components/Table';
  import { useTable } from '@/hooks/web/useTable';
  import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas';
  import { ref, reactive, unref, toRef, onMounted, watch } from 'vue';
  import {
    getCenterListApi,
    getPendingTargetServerlistApi,
    getSourceServerIpListApi
  } from '@/api/comparisonSettings';
  import type { IQueryData } from '@/api/comparisonSettings/types';
  import {
    ElMessage,
    ElTable,
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    FormInstance
  } from 'element-plus';
  import config from '@/config/axios/config';
  const { t } = useI18n();
  const { result_code } = config;

  const props = defineProps({
    businessSystemId: {
      type: String,
      default: () => ''
    }
  });

  const crudSchemas = reactive<CrudSchema[]>([
    {
      field: 'selection', //复选框
      table: {
        type: 'selection'
      }
    },
    {
      field: 'index',
      label: t('demo.index'),
      type: 'index',
      detail: {
        hidden: true
      }
    },
    {
      field: 'computerIp',
      label: t('一致性比对.目标服务器IP')
    },
    {
      field: 'centerName',
      label: t('一致性比对.所属中心')
    }
  ]);
  const { allSchemas } = useCrudSchemas(crudSchemas);
  //列表定义
  const queryData = reactive<IQueryData>({
    queryParam: {},
    pageNum: 1,
    pageSize: 10
  });
  const searchParams = toRef(queryData, 'queryParam');
  const { tableRegister, tableState, tableMethods } = useTable({
    immediate: false,
    fetchDataApi: async () => {
      const { currentPage, pageSize } = tableState;
      queryData.pageNum = unref(currentPage);
      queryData.pageSize = unref(pageSize);
      const res = await getPendingTargetServerlistApi(queryData);
      return {
        list: res.data.list,
        total: res.data.total
      };
    }
  });
  const { loading, dataList, total, currentPage, pageSize } = tableState;
  const { refresh, getList, getElTableExpose } = tableMethods;

  const formRef = ref<FormInstance>();
  const ruleForm = reactive({
    sourceCenterId: '',
    sourceComputerId: '',
    targetCenterId: ''
  });
  const rules = reactive({
    sourceCenterId: [{ required: true, message: t('一致性比对.请选择源中心'), trigger: 'change' }],
    sourceComputerId: [
      { required: true, message: t('一致性比对.请选择源服务器IP'), trigger: 'change' }
    ],
    targetCenterId: [{ required: true, message: t('一致性比对.请选择目标中心'), trigger: 'change' }]
  });
  const centerDataList = ref<any[]>([]); //源中心
  const sourceServerIpList = ref<any[]>([]); //源服务器IP列表
  const targetCenterList = ref<any[]>([]); //目标中心列表
  const batchCount = ref<number>(0);
  let elTableExpose = ref<ComponentRef<typeof ElTable>>();

  //查询
  const searchParamsFn = () => {
    elTableExpose.value!.clearSelection();
    currentPage.value = 1;
    searchParams.value = {
      sourceCenterId: ruleForm.sourceCenterId,
      sourceComputerId: ruleForm.sourceComputerId,
      targetCenterId: ruleForm.targetCenterId,
      businessSystemId: props.businessSystemId
    };
    getList();
  };

  // 获取中心列表
  const getCenterListFn = async () => {
    const res = await getCenterListApi({}).catch(() => {});
    centerDataList.value = res?.data || [];
  };

  getCenterListFn();

  // 获取源服务器IP列表
  const getSourceServerIpList = async (val: string) => {
    if (val) {
      const res = await getSourceServerIpListApi({
        businessSystemId: props.businessSystemId,
        centerId: val
      });
      if (res?.code === result_code) {
        sourceServerIpList.value = res?.data || [];
      } else {
        sourceServerIpList.value = [];
      }
    }
  };

  // 改变源中心
  const changeSourceCenter = (val: string) => {
    ruleForm.sourceComputerId = '';
    ruleForm.targetCenterId = '';
    dataList.value = [];
    elTableExpose.value!.clearSelection();
    targetCenterList.value = [];
    getSourceServerIpList(val);
  };

  // 改变源服务器IP
  const changeSourceComputerIdFn = () => {
    ruleForm.targetCenterId = '';
    dataList.value = [];
    elTableExpose.value!.clearSelection();
    targetCenterList.value = [...centerDataList.value];
  };

  const emit = defineEmits(['saveServer']);
  const submit = async () => {
    let valid = await formRef.value?.validate().catch(() => {});
    if (valid) {
      const selectedRows = elTableExpose.value?.getSelectionRows();
      if (selectedRows?.length == 0) {
        ElMessage.warning(t('一致性比对.请选择目标服务器'));
        return;
      }
      let selectedItemIds = selectedRows.map((v: any) => v.computerId) || [];
      let params = {
        businessSystemId: props.businessSystemId,
        ...ruleForm,
        targetComputerIdList: selectedItemIds
      };
      emit('saveServer', params);
    }
  };

  onMounted(async () => {
    elTableExpose.value = await getElTableExpose();
    watch(
      () => elTableExpose.value?.getSelectionRows(),
      (val) => {
        batchCount.value = val.length;
      },
      { deep: true }
    );
  });

  defineExpose({ submit });
</script>

<template>
  <el-form
    ref="formRef"
    :model="ruleForm"
    :rules="rules"
    :inline="false"
    label-width="auto"
    @submit.prevent
  >
    <el-form-item :label="t('一致性比对.源中心')" prop="sourceCenterId" style="width: 100%">
      <el-select
        v-model="ruleForm.sourceCenterId"
        @change="changeSourceCenter"
        filterable
        style="width: 100%"
        :placeholder="t('一致性比对.请选择源中心')"
      >
        <el-option
          :label="item.name"
          :value="item.id"
          v-for="item in centerDataList"
          :key="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item :label="t('一致性比对.源服务器IP')" prop="sourceComputerId" style="width: 100%">
      <el-select
        v-model="ruleForm.sourceComputerId"
        style="width: 100%"
        :placeholder="t('一致性比对.请选择源服务器IP')"
        @change="changeSourceComputerIdFn"
      >
        <el-option
          :label="item.computerIp"
          :value="item.computerId"
          v-for="item in sourceServerIpList"
          :key="item.computerId"
        />
      </el-select>
    </el-form-item>
    <el-form-item :label="t('一致性比对.目标中心')" prop="targetCenterId" style="width: 100%">
      <el-select
        v-model="ruleForm.targetCenterId"
        @change="searchParamsFn"
        filterable
        style="width: 100%"
        :placeholder="t('一致性比对.请选择目标中心')"
      >
        <el-option
          :label="item.name"
          :value="item.id"
          v-for="item in targetCenterList"
          :key="item.id"
        />
      </el-select>
    </el-form-item>
  </el-form>
  <h2 style="font-size: 14px" class="mb-1 font-bold">{{ t('一致性比对.目标服务器') }}</h2>
  <Table
    v-model:pageSize="pageSize"
    v-model:currentPage="currentPage"
    :columns="allSchemas.tableColumns"
    :data="dataList"
    :loading="loading"
    :pagination="{ total: total }"
    @register="tableRegister"
    @refresh="refresh"
    :reserve-selection="true"
    showAction
    :batch-count="batchCount"
    row-key="id"
    :active-u-i-d="'SystemConfigurationCheckEquipment'"
    :emptyText="t('common.暂无数据')"
  />
</template>
