import { defineStore } from 'pinia';
import { useCache } from '@/hooks/web/useCache';
import { humpToUnderline, mix, setCssVar } from '@/utils';

const { wsCache } = useCache();

export type ThemeTypes = {
  elColorPrimary?: string;
};
export interface AppState {
  sizeMap: ElememtPlusSize[];
  mobile: boolean;
  greyMode: boolean;
  isDark: boolean;
  currentSize: ElememtPlusSize;
  theme: ThemeTypes;
}
export const appModules: AppState = {
  sizeMap: ['default', 'large', 'small'],
  mobile: false, // 是否是移动端
  greyMode: false, // 是否开始灰色模式，用于特殊悼念日
  isDark: wsCache.get('isDark') || false, // 是否是暗黑模式
  currentSize: wsCache.get('default') || 'default', // 组件尺寸
  theme: wsCache.get('theme') || {
    // 主题色
    elColorPrimary: '#409eff'
  }
};
/** 色阶 */
export const LEVELS = [3, 5, 7, 8, 9];
export enum ThemeEnum {
  /** 白色 */
  WHITE = '#FFFFFF',
  /** 黑色 */
  BLACK = '#000000'
}
export const useAppStore = defineStore({
  id: 'pinia-app',
  state: (): AppState => appModules,
  getters: {
    getGreyMode(): boolean {
      return this.greyMode;
    },
    getIsDark(): boolean {
      return this.isDark;
    },
    getCurrentSize(): ElememtPlusSize {
      return this.currentSize;
    },
    getSizeMap(): ElememtPlusSize[] {
      return this.sizeMap;
    },
    getMobile(): boolean {
      return this.mobile;
    },
    getTheme(): ThemeTypes {
      return this.theme;
    }
  },
  actions: {
    setIsDark(isDark: boolean) {
      this.isDark = isDark;
      if (this.isDark) {
        document.documentElement.classList.add('dark');
        document.documentElement.classList.remove('light');
      } else {
        document.documentElement.classList.add('light');
        document.documentElement.classList.remove('dark');
      }
    },
    setCurrentSize(currentSize: ElememtPlusSize) {
      this.currentSize = currentSize;
    },
    setMobile(mobile: boolean) {
      this.mobile = mobile;
    },
    setTheme(theme: ThemeTypes) {
      this.theme = Object.assign(this.theme, theme);
    },
    setCssVarTheme() {
      for (const key in this.theme) {
        if (key === 'elColorPrimary') {
          this.setColor(`--${humpToUnderline(key)}`, this.theme[key]);
        } else {
          setCssVar(`--${humpToUnderline(key)}`, this.theme[key]);
        }
      }
    },
    setColor(key, color) {
      // 设置主要颜色变量
      setCssVar(key, color);
      // 循环设置色阶颜色
      LEVELS.forEach((level) => {
        setCssVar(key + '-light-' + level, mix(ThemeEnum.WHITE, color, level * 0.1));
      });
      // 设置主要暗色
      setCssVar(key + '-dark-2', mix(ThemeEnum.BLACK, color, 0.2));
    }
  }
});
