<script setup lang="tsx">
  import { ref } from 'vue';
  import { ElCard } from 'element-plus';
  import SourceEquipemnt from './config/SourceEquipemnt.vue';
  import RuleTable from './config/RuleTable.vue';

  defineProps({
    currentSystem: {
      type: Object,
      default: () => {}
    }
  });
  const currentEquipemnt = ref<any>({});

  const rowSystemClick = (row: any) => {
    currentEquipemnt.value = row;
  };
</script>
<template>
  <div class="flex gap-2">
    <el-card style="width: 45%">
      <SourceEquipemnt :currentSystemData="currentSystem" @row-click="rowSystemClick" />
    </el-card>
    <el-card style="width: 55%">
      <RuleTable :current-row-equipemnt="currentEquipemnt" />
    </el-card>
  </div>
</template>
