{"name": "ideal-application-base", "version": "0.0.0", "private": true, "config": {"registry": "https://registry.npm.taobao.org/"}, "scripts": {"i": "pnpm install", "dev": "pnpm run lint:format && vite --mode base", "build:pro": "vite build --mode pro", "build:dev": "npm run ts:check && vite build --mode dev", "build:test": "npm run ts:check && vite build --mode test", "serve:pro": "vite preview --mode pro", "serve:dev": "vite preview --mode dev", "serve:test": "vite preview --mode test", "test:unit": "vitest", "build-only": "vite build", "ts:check": "vue-tsc --noEmit --composite false", "npm:check": "npx npm-check-updates", "clean": "npx rimraf node_modules", "clean:cache": "npx rimraf node_modules/.cache", "lint:eslint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lint:format": "prettier --write --log-level warn \"src/**/*.{js,ts,json,cjs,css,less,vue,html,md,tsx}\"", "lint:style": "stylelint --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.js", "postinstall": "husky install", "preinstall": "npx only-allow pnpm"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@iconify/iconify": "^3.1.1", "@iconify/vue": "^4.1.1", "@vueuse/core": "^10.4.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "5.1.10", "@zxcvbn-ts/core": "^3.0.4", "axios": "1.7.9", "dayjs": "^1.11.13", "element-plus": "2.6.2", "json-bigint": "^1.0.0", "less": "^4.1.3", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "monaco-editor": "^0.52.2", "pinia": "^2.1.6", "pinia-plugin-persist": "^1.0.0", "qs": "^6.10.3", "sortablejs": "^1.15.0", "vue": "3.3.4", "vue-i18n": "11.0.1", "vue-router": "^4.2.5", "vue-types": "^5.1.1", "vuedraggable": "^4.1.0", "web-storage-cache": "^1.1.1"}, "devDependencies": {"@commitlint/cli": "19.6.1", "@commitlint/config-conventional": "19.6.0", "@iconify/json": "2.2.293", "@intlify/unplugin-vue-i18n": "6.0.3", "@purge-icons/generated": "^0.9.0", "@rushstack/eslint-patch": "^1.2.0", "@types/jsdom": "^21.1.0", "@types/lodash-es": "^4.17.8", "@types/node": "^20.6.5", "@types/qs": "^6.9.8", "@types/sortablejs": "^1.15.2", "@typescript-eslint/eslint-plugin": "^6.7.2", "@typescript-eslint/parser": "^6.7.2", "@vitejs/plugin-vue": "^4.3.4", "@vitejs/plugin-vue-jsx": "^3.0.2", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.2", "@vue/test-utils": "^2.3.0", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.16", "commitizen": "^4.2.4", "eslint": "^8.50.0", "eslint-config-prettier": "^8.8.0", "eslint-define-config": "^1.5.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^9.9.0", "husky": "^8.0.1", "jsdom": "^21.1.0", "lint-staged": "^14.0.1", "npm-run-all": "^4.1.5", "prettier": "^3.0.3", "rimraf": "^3.0.2", "stylelint": "^15.10.3", "stylelint-config-html": "^1.0.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-standard": "^25.0.0", "stylelint-order": "^5.0.0", "terser": "^5.20.0", "typescript": "5.2.2", "unplugin-vue-define-options": "^1.3.12", "vite": "6.3.2", "vite-plugin-eslint": "^1.8.1", "vite-plugin-purge-icons": "^0.9.2", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-windicss": "^1.9.0", "vitest": "^0.29.1", "vue-tsc": "^1.8.13", "windicss": "^3.5.6", "windicss-analysis": "^0.3.5"}, "engines": {"node": ">= 14.18.0"}, "pnpm": {"overrides": {"semver@5.7.1": "5.7.2", "follow-redirects@1.15.2": "1.15.6", "es5-ext@0.10.62": "0.10.63"}}}