//查询
export interface IQueryData {
  queryParam: any;
  pageNum: number;
  pageSize: number;
}
export interface ISystemConfiguration {
  id: string;
  businessSystemId: string;
  businessSystemName: string;
  businessSystemDesc: string;
  creatorName: string;
  createTime: string;
}

export interface IEquipment {
  id: string;
  computerName: string;
  computerIp: string;
  computerId: string;
  centerId: string;
  centerName: string;
}
