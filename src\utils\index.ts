import type { App, Plugin } from 'vue';
import { useCache } from '@/hooks/web/useCache';
const { wsCache } = useCache();

/**
 *
 * @param component 需要注册的组件
 * @param alias 组件别名
 * @returns any
 */
export const withInstall = <T>(component: T, alias?: string) => {
  const comp = component as any;
  comp.install = (app: App) => {
    // @ts-ignore
    app.component(comp.name || comp.displayName, component);
    if (alias) {
      app.config.globalProperties[alias] = component;
    }
  };
  return component as T & Plugin;
};

/**
 * @param str 需要转下划线的驼峰字符串
 * @returns 字符串下划线
 */
export const humpToUnderline = (str: string): string => {
  return str.replace(/([A-Z])/g, '-$1').toLowerCase();
};

/**
 * @param str 需要转驼峰的下划线字符串
 * @returns 字符串驼峰
 */
export const underlineToHump = (str: string): string => {
  return str.replace(/\-(\w)/g, (_, letter: string) => {
    return letter.toUpperCase();
  });
};

/**
 * 驼峰转横杠
 */
export const humpToDash = (str: string): string => {
  return str.replace(/([A-Z])/g, '-$1').toLowerCase();
};

export const setCssVar = (prop: string, val: any, dom = document.documentElement) => {
  dom.style.setProperty(prop, val);
};

/**
 * 查找数组对象的某个下标
 * @param {Array} ary 查找的数组
 * @param {Functon} fn 判断的方法
 */
// eslint-disable-next-line
export const findIndex = <T = Recordable>(ary: Array<T>, fn: Fn): number => {
  if (ary.findIndex) {
    return ary.findIndex(fn);
  }
  let index = -1;
  ary.some((item: T, i: number, ary: Array<T>) => {
    const ret: T = fn(item, i, ary);
    if (ret) {
      index = i;
      return ret;
    }
  });
  return index;
};

export const trim = (str: string) => {
  return str.replace(/(^\s*)|(\s*$)/g, '');
};

/**
 * @param {Date | number | string} time 需要转换的时间
 * @param {String} fmt 需要转换的格式 如 yyyy-MM-dd、yyyy-MM-dd HH:mm:ss
 */
export function formatTime(time: Date | number | string, fmt: string) {
  if (!time) return '';
  else {
    const date = new Date(time);
    const o = {
      'M+': date.getMonth() + 1,
      'd+': date.getDate(),
      'H+': date.getHours(),
      'm+': date.getMinutes(),
      's+': date.getSeconds(),
      'q+': Math.floor((date.getMonth() + 3) / 3),
      S: date.getMilliseconds()
    };
    if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    for (const k in o) {
      if (new RegExp('(' + k + ')').test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
        );
      }
    }
    return fmt;
  }
}

/**
 * 生成随机字符串
 */
export function toAnyString() {
  const str: string = 'xxxxx-xxxxx-4xxxx-yxxxx-xxxxx'.replace(/[xy]/g, (c: string) => {
    const r: number = (Math.random() * 16) | 0;
    const v: number = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString();
  });
  return str;
}
/**
 * 深拷贝
 * @param object 要拷贝的对象
 * @param default_val
 * @returns
 */
export const objectDeepClone = (object: object, default_val: any = {}) => {
  if (!object) {
    return default_val;
  }
  return JSON.parse(JSON.stringify(object));
};

/**
 * 获取表格字段
 * @param routeName
 * @param columns
 */
export function getTableBook(routeName: string, columns: any) {
  const tableBook: object = wsCache.get('routeColsName') || {};
  const columns2Save: any[] = [];
  if (tableBook[routeName]) {
    tableBook[routeName]['select'].map((parent: any) => {
      columns.map((child: any) => {
        if (child.field == parent.id) {
          child.key = child.field;
          columns2Save.push(child);
        }
      });
    });
  } else {
    tableBook[routeName] = {};
    const selectArr: any[] = [];
    const all: any[] = [];
    columns.map((item: any) => {
      selectArr.push({ label: item.label, key: item.field, id: item.field });
      all.push(item.field);
    });
    columns.map((item: any) => {
      item.key = item.field;
      columns2Save.push(item);
    });
    tableBook[routeName]['select'] = selectArr;
    tableBook[routeName]['all'] = all;
    wsCache.set('routeColsName', tableBook);
  }
  const allOption = tableBook[routeName]['all'];
  return { columns2Save, allOption };
}

/**
 * 首字母大写
 */
export function firstUpperCase(str: string) {
  return str.toLowerCase().replace(/( |^)[a-z]/g, (L) => L.toUpperCase());
}
/**
 * Mixes two colors.
 *
 * @param {string} color1 - The first color, should be a 6-digit hexadecimal color code starting with `#`.
 * @param {string} color2 - The second color, should be a 6-digit hexadecimal color code starting with `#`.
 * @param {number} [weight=0.5] - The weight of color1 in the mix, should be a number between 0 and 1, where 0 represents 100% of color2, and 1 represents 100% of color1.
 * @returns {string} The mixed color, a 6-digit hexadecimal color code starting with `#`.
 */
export const mix = (color1: string, color2: string, weight: number = 0.5): string => {
  let color = '#';
  for (let i = 0; i <= 2; i++) {
    const c1 = parseInt(color1.substring(1 + i * 2, 3 + i * 2), 16);
    const c2 = parseInt(color2.substring(1 + i * 2, 3 + i * 2), 16);
    const c = Math.round(c1 * weight + c2 * (1 - weight));
    color += c.toString(16).padStart(2, '0');
  }
  return color;
};
