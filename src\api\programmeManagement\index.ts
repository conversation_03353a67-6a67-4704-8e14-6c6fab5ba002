import request from '@/config/axios';

// 获取方案列表
export const getProgrammeListApi = (data?: any) => {
  return request.post({ url: '/plan/list', data });
};

export const removeProgrammeApi = (data?: any) => {
  return request.post({ url: '/plan/remove', data });
};

// 新增方案
export const saveProgrammeApi = (data?: any) => {
  return request.post({ url: '/plan/save', data });
};

export const updateProgrammeApi = (data?: any) => {
  return request.post({ url: '/plan/update', data });
};
// 根据方案获取业务系统
export const getBusinessSystemByPlanIdApi = (data?: any) => {
  return request.post({ url: '/planRelation/systemList', data });
};

// 方案解绑业务系统
export const removePlanRelationApi = (data?: any) => {
  return request.post({ url: '/planRelation/remove', data });
};

export const getPlanRelationSystemlistApi = (data?: any) => {
  return request.post({ url: '/planRelation/pendingSystemList', data });
};

// 查询方案系统下源目标设备信息
export const getPlanRelationSystemNodeListApi = (data?: any) => {
  return request.post({ url: '/planRelation/systemNodeList', data });
};

export const getRelationRuleListApi = (data?: any) => {
  return request.post({ url: '/relation/list', data });
};

// 方案绑定系统信息
export const savePlanRelationSystemApi = (data?: any) => {
  return request.post({ url: '/planRelation/save', data });
};

export const startComparingApi = (data?: any) => {
  return request.post({ url: '/start/plans', data });
};

// 周期比对
export const circleComparingApi = (data?: any) => {
  return request.post({ url: '/task/create', data });
};

export const nodesComparingApi = (data?: any) => {
  return request.post({ url: '/start/nodes', data });
};

export const rulesComparingApi = (data?: any) => {
  return request.post({ url: '/start/rules', data });
};
