<script setup lang="tsx">
  import { nextTick, ref } from 'vue';
  import { useI18n } from '@/hooks/web/useI18n';
  import 'monaco-editor/min/vs/editor/editor.main.css';
  import * as monaco from 'monaco-editor/esm/vs/editor/editor.api.js';
  import config from '@/config/axios/config';
  import { getComparisonContentApi } from '@/api/comparisonResults';
  const { result_code } = config;
  const { t } = useI18n();

  const props = defineProps({
    currentRow: {
      type: Object,
      default: () => ({})
    }
  });

  const sourceInfo = ref<any>({
    sourceComputer: props.currentRow?.sourceComputerIp,
    path: props.currentRow?.sourcePath
  });
  const sourceData = ref(''); //源服务器数据
  const targetInfo = ref<any>({
    targetComputer: props.currentRow?.targetComputerIp,
    path: props.currentRow?.path
  });
  const targetData = ref(''); //目标服务器数据

  const getComparisonData = async () => {
    const params = { flowId: props.currentRow?.flowId };
    const res = await getComparisonContentApi(params)
      .catch(() => {})
      .finally(() => {});
    if (res?.code === result_code) {
      sourceData.value = res?.data?.sourceContent;
      targetData.value = res?.data?.targetContent;
    }
  };

  const comparisonFn = () => {
    nextTick(() => {
      var originalModel = monaco.editor.createModel(sourceData.value, 'text/plain');
      var modifiedModel = monaco.editor.createModel(targetData.value, 'text/plain');
      var diffEditor = monaco.editor.createDiffEditor(
        document.getElementById('comparison-results-container') as any,
        {
          // theme: 'vs-dark',
          automaticLayout: true
        }
      );
      diffEditor.setModel({
        original: originalModel,
        modified: modifiedModel
      });

      window.addEventListener('resize', () => {
        diffEditor.layout();
      });
    });
  };
  comparisonFn();
  getComparisonData();
</script>
<template>
  <div>
    <div class="flex mb-2">
      <div style="width: 50%; overflow-wrap: break-word; margin-right: 8px">
        <span class="mr-2">{{ t('一致性比对.源服务器') }}：{{ sourceInfo.sourceComputer }}</span>
        <span>{{ t('一致性比对.路径') }}：{{ sourceInfo.path }}</span>
      </div>
      <div style="width: 50%; overflow-wrap: break-word">
        <span class="mr-2">{{ t('一致性比对.目标服务器') }}：{{ targetInfo.targetComputer }} </span>
        <span>{{ t('一致性比对.路径') }}：{{ targetInfo.path }}</span>
      </div>
    </div>
    <div id="comparison-results-container" style="width: 100%; height: calc(100vh - 150px)"></div>
  </div>
</template>

<style lang="less">
  #comparison-results-container {
    .monaco-editor.no-user-select .lines-content,
    .monaco-editor.no-user-select .view-line,
    .monaco-editor.no-user-select .view-lines {
      user-select: text !important;
    }
  }
</style>
