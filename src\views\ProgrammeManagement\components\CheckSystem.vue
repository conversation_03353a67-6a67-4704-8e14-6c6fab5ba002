<script setup lang="tsx">
  import { useI18n } from '@/hooks/web/useI18n';
  import { Search } from '@/components/Search';
  import { Table } from '@/components/Table';
  import { useTable } from '@/hooks/web/useTable';
  import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas';
  import { ref, reactive, unref, toRef, onMounted, watch } from 'vue';
  import { getPlanRelationSystemlistApi } from '@/api/programmeManagement';
  import type { IQueryData } from '@/api/programmeManagement/types';
  import { ElMessage, ElTable } from 'element-plus';
  const { t } = useI18n();
  const props = defineProps({
    currentProgramme: {
      type: Object,
      default: () => {}
    }
  });

  const crudSchemas = reactive<CrudSchema[]>([
    {
      field: 'selection', //复选框
      table: {
        type: 'selection'
      }
    },
    {
      field: 'index',
      label: t('demo.index'),
      type: 'index',
      detail: {
        hidden: true
      }
    },
    {
      field: 'businessSystemName',
      label: t('一致性比对.业务系统'),
      search: {
        component: 'Input'
      }
    },
    {
      field: 'businessSystemCode',
      label: t('一致性比对.系统缩写')
    },
    {
      field: 'creatorName',
      label: t('一致性比对.创建人')
    },
    {
      field: 'createTime',
      label: t('一致性比对.创建时间')
    }
  ]);
  const { allSchemas } = useCrudSchemas(crudSchemas);
  //列表定义
  const queryData = reactive<IQueryData>({
    queryParam: { planId: props.currentProgramme?.id },
    pageNum: 1,
    pageSize: 10
  });
  const searchParams = toRef(queryData, 'queryParam');
  const { tableRegister, tableState, tableMethods } = useTable({
    fetchDataApi: async () => {
      const { currentPage, pageSize } = tableState;
      queryData.pageNum = unref(currentPage);
      queryData.pageSize = unref(pageSize);
      const res = await getPlanRelationSystemlistApi(queryData);
      return {
        list: res.data.list,
        total: res.data.total
      };
    }
  });
  const { loading, dataList, total, currentPage, pageSize } = tableState;
  const { getList, refresh, getElTableExpose } = tableMethods;

  let elTableExpose = ref<ComponentRef<typeof ElTable>>();
  const batchCount = ref<number>(0);

  //查询
  const setSearchParams = (params: any) => {
    searchParams.value = { ...params, planId: props.currentProgramme?.id };
    getList();
  };

  onMounted(async () => {
    elTableExpose.value = await getElTableExpose();
    watch(
      () => elTableExpose.value?.getSelectionRows(),
      (val) => {
        batchCount.value = val.length;
      },
      { deep: true }
    );
  });

  const emit = defineEmits(['checkSystem']);
  const submit = () => {
    const selectedRows = elTableExpose.value?.getSelectionRows();
    if (selectedRows?.length == 0) {
      ElMessage.warning(t('一致性比对.请选择业务系统'));
      return;
    }
    emit('checkSystem', selectedRows);
  };

  defineExpose({ submit });
</script>

<template>
  <Search :schema="allSchemas.searchSchema" @search="setSearchParams" @reset="setSearchParams" />
  <Table
    v-model:pageSize="pageSize"
    v-model:currentPage="currentPage"
    :columns="allSchemas.tableColumns"
    :data="dataList"
    :loading="loading"
    :pagination="{ total: total }"
    @register="tableRegister"
    @refresh="refresh"
    :reserve-selection="true"
    :batch-count="batchCount"
    row-key="businessSystemId"
    :active-u-i-d="'ProgrammeManagementCheckSystem'"
    :emptyText="t('common.暂无数据')"
  />
</template>
