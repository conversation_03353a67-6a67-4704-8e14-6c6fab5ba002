<script setup lang="tsx">
  import { Form, FormSchema } from '@/components/Form';
  import { useForm } from '@/hooks/web/useForm';
  import { PropType, watch } from 'vue';
  import type { IProgrammeTable } from '@/api/programmeManagement/types';

  const props = defineProps({
    currentRow: {
      type: Object as PropType<Nullable<IProgrammeTable>>,
      default: () => {}
    },
    formSchema: {
      type: Array as PropType<FormSchema[]>,
      default: () => []
    }
  });

  const { formRegister, formMethods } = useForm();
  const { setValues, getFormData, getElFormExpose } = formMethods;

  const submit = async () => {
    const elForm = await getElFormExpose();
    const valid = await elForm?.validate().catch((err) => {
      console.log(err);
    });
    if (valid) {
      return await getFormData();
    }
  };

  watch(
    () => props.currentRow,
    (currentRow) => {
      if (!currentRow) return;
      setValues(currentRow);
    },
    {
      deep: true, //深度监听
      immediate: true //初始化时立即执行一次
    }
  );

  defineExpose({
    submit
  });
</script>

<template>
  <Form @register="formRegister" :schema="formSchema" />
</template>
