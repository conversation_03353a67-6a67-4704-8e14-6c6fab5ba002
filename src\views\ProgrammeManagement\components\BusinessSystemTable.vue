<script setup lang="tsx">
  import { useI18n } from '@/hooks/web/useI18n';
  import { Search } from '@/components/Search';
  import { Table } from '@/components/Table';
  import type { BatchOption } from '@/components/Table/src/types';
  import { useTable } from '@/hooks/web/useTable';
  import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas';
  import { ref, reactive, unref, toRef, onMounted, watch } from 'vue';
  import ButtonGroup from '@/components/ButtonGroup/src/ButtonGroup.vue';
  import type { ButtonGroupSchema } from '@/components/ButtonGroup/src/types';
  import {
    getBusinessSystemByPlanIdApi,
    removePlanRelationApi,
    savePlanRelationSystemApi
  } from '@/api/programmeManagement';
  import type { IQueryData, ISystemTable } from '@/api/programmeManagement/types';
  import { ElT<PERSON>, ElDrawer, ElButton, ElMessage, ElPopconfirm, ElDivider } from 'element-plus';
  import CheckSystem from './CheckSystem.vue';
  import ConfigTable from './ConfigTable.vue';
  import config from '@/config/axios/config';
  const { t } = useI18n();
  const { result_code } = config;

  const props = defineProps({
    currentRowProgramme: {
      type: Object,
      default: () => {}
    }
  });

  const crudSchemas = reactive<CrudSchema[]>([
    {
      field: 'selection', //复选框
      table: {
        type: 'selection'
      }
    },
    {
      field: 'index',
      label: t('demo.index'),
      type: 'index'
    },
    {
      field: 'businessSystemName',
      label: t('一致性比对.业务系统'),
      minWidth: 120,
      search: {
        component: 'Input'
      }
    },
    {
      field: 'businessSystemCode',
      minWidth: 120,
      label: t('一致性比对.系统缩写')
    },
    {
      field: 'createTime',
      label: t('一致性比对.创建时间'),
      width: 170
    },
    {
      field: 'action',
      width: 120,
      label: t('common.handle'),
      table: {
        fixed: 'right',
        slots: {
          default: (data: any) => {
            return (
              <div>
                <ElPopconfirm title={t('common.delMessage')} onConfirm={() => delData(data.row)}>
                  {{
                    reference: () => {
                      return (
                        <ElButton link type="danger" v-hasPerm="removeSystemOfPlan">
                          {t('common.delete')}
                        </ElButton>
                      );
                    }
                  }}
                </ElPopconfirm>
                <ElDivider direction="vertical" v-hasPerm="removeSystemOfPlan" />
                <ElButton
                  link
                  type="primary"
                  onClick={() => onConfigFn(data.row)}
                  v-hasPerm="configOfSystem"
                >
                  {t('一致性比对.配置')}
                </ElButton>
              </div>
            );
          }
        }
      }
    }
  ]);
  const { allSchemas } = useCrudSchemas(crudSchemas);
  //列表定义
  const queryData = reactive<IQueryData>({
    queryParam: { planId: props.currentRowProgramme.id },
    pageNum: 1,
    pageSize: 10
  });
  const searchParams = toRef(queryData, 'queryParam');
  const selectedItemIds = ref<number[]>([]);
  const { tableRegister, tableState, tableMethods } = useTable({
    immediate: false,
    fetchDataApi: async () => {
      const { currentPage, pageSize } = tableState;
      queryData.pageNum = unref(currentPage);
      queryData.pageSize = unref(pageSize);
      const res = await getBusinessSystemByPlanIdApi(queryData);
      return {
        list: res.data.list,
        total: res.data.total
      };
    },
    fetchDelApi: async () => {
      const res = await removePlanRelationApi(selectedItemIds.value);
      return !!res;
    }
  });
  const { loading, dataList, total, currentPage, pageSize } = tableState;
  const { getList, getElTableExpose, refresh, delList } = tableMethods;

  let elTableExpose = ref<ComponentRef<typeof ElTable>>();
  let checksystemRef = ref<InstanceType<typeof CheckSystem>>();
  const currentSystemInfo = ref<any>({});
  const batchCount = ref<number>(0);
  const actionVisible = ref(false);
  const saveLoading = ref(false);
  const configVisible = ref(false);

  /*按钮组定义*/
  const buttonSchema = ref<ButtonGroupSchema[]>([
    {
      type: 'primary',
      field: 'add',
      name: t('common.add'),
      perm: 'saveSystemOfPlan',
      option: () => {
        addActionFn();
      }
    }
  ]);
  /*批处理表格*/
  const batchOptions = ref<BatchOption[]>([
    {
      name: t('common.delete'),
      icon: 'mdi:trash-can-outline',
      title: t('common.delMessage'),
      perm: 'removeSystemOfPlan',
      option: () => {
        delData(null);
      }
    }
  ]);

  //查询
  const setSearchParams = (params: any) => {
    searchParams.value = { ...params, planId: props.currentRowProgramme.id };
    getList();
  };

  const addActionFn = () => {
    actionVisible.value = true;
  };

  const saveFn = async () => {
    checksystemRef.value!.submit(); // 调用子组件的方法
  };

  const submitCheckSystem = async (checkDatas: any) => {
    let params = {
      envcPlanId: props.currentRowProgramme.id,
      businessSystemIdList: checkDatas.map((item: any) => item.businessSystemId)
    };
    saveLoading.value = true;
    const res: any = await savePlanRelationSystemApi(params)
      .catch(() => {})
      .finally(() => {
        saveLoading.value = false;
      });
    if (res && res.code == result_code) {
      ElMessage.success(t('common.addSuccess'));
      actionVisible.value = false;
      currentPage.value = 1;
      await getList();
    } else {
      ElMessage.error(res?.message);
    }
  };

  //删除
  const delLoading = ref(false);
  const delData = async (row: ISystemTable | null) => {
    selectedItemIds.value = row
      ? [row.id]
      : elTableExpose.value!.getSelectionRows().map((v: ISystemTable) => v.id) || [];
    delLoading.value = true;
    await delList(unref(selectedItemIds).length).finally(() => {
      delLoading.value = false;
    });
  };

  const onConfigFn = (row: ISystemTable) => {
    currentSystemInfo.value = row;
    configVisible.value = true;
  };

  onMounted(async () => {
    elTableExpose.value = await getElTableExpose();
    watch(
      () => elTableExpose.value?.getSelectionRows(),
      (val) => {
        batchCount.value = val.length;
      },
      { deep: true }
    );
  });

  watch(
    () => props.currentRowProgramme,
    (val) => {
      if (Object.keys(val).length == 0) {
        dataList.value = [];
        return;
      }
      queryData.queryParam = {
        planId: val.id
      };
      getList();
    },
    { deep: true, immediate: true }
  );
</script>

<template>
  <Search :schema="allSchemas.searchSchema" @search="setSearchParams" @reset="setSearchParams" />
  <ButtonGroup :schema="buttonSchema" />
  <Table
    class="mt-1"
    v-model:pageSize="pageSize"
    v-model:currentPage="currentPage"
    :columns="allSchemas.tableColumns"
    :data="dataList"
    :loading="loading"
    :pagination="{ total: total }"
    @register="tableRegister"
    @refresh="refresh"
    showAction
    :batch-count="batchCount"
    :batch-options="batchOptions"
    :reserve-selection="true"
    row-key="id"
    :active-u-i-d="'ProgrammeManagementSystemTable'"
    :emptyText="t('common.暂无数据')"
  />
  <!-- 选择业务系统 -->
  <ElDrawer
    v-model="actionVisible"
    :title="t('common.add') + t('一致性比对.业务系统')"
    destroy-on-close
    size="50%"
    class="customForm"
  >
    <CheckSystem
      ref="checksystemRef"
      :current-programme="currentRowProgramme"
      @check-system="submitCheckSystem"
    />
    <template #footer>
      <ElButton type="primary" :loading="saveLoading" @click="saveFn">
        {{ t('common.ok') }}
      </ElButton>
      <ElButton @click="actionVisible = false">{{ t('common.cancel') }}</ElButton>
    </template>
  </ElDrawer>
  <!-- 配置 -->
  <ElDrawer
    v-if="configVisible"
    v-model="configVisible"
    :title="t('一致性比对.配置')"
    destroy-on-close
    size="90%"
  >
    <ConfigTable :current-system="currentSystemInfo" />
  </ElDrawer>
</template>
