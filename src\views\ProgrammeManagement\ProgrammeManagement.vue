<script setup lang="tsx">
  import { ref } from 'vue';
  import { ElCard } from 'element-plus';
  import BusinessSystemTable from './components/BusinessSystemTable.vue';
  import ProgrammeTable from './components/ProgrammeTable.vue';
  import { useI18n } from '@/hooks/web/useI18n';
  const { t } = useI18n();

  const currentProgramme = ref<any>({});

  const rowSystemClick = (row: any) => {
    currentProgramme.value = row;
  };
</script>
<template>
  <div class="flex gap-2">
    <el-card style="width: 55%">
      <template #header>{{ t('一致性比对.方案') }}</template>
      <ProgrammeTable @row-click="rowSystemClick" />
    </el-card>
    <el-card style="width: 45%">
      <template #header>{{ t('一致性比对.业务系统') }}</template>
      <BusinessSystemTable :current-row-programme="currentProgramme" />
    </el-card>
  </div>
</template>
