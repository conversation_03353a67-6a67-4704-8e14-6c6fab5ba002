import '@/plugins/windi.css'; // 引入windi css
import '@/plugins/svgIcon'; // 导入全局的svg图标
import { setupI18n } from '@/plugins/vueI18n'; // 初始化多语言
import { createApp } from 'vue';
import { setupElementPlus } from '@/plugins/elementPlus'; // 引入element-plus
import App from './App.vue';
import router from './router';
import { setupGlobCom } from '@/components'; // 全局组件
import { setupStore } from '@/store'; // 引入状态管理
import { setupPermission } from '@/directives'; // 引入权限
import 'element-plus/dist/index.css';
const setupAll = () => {
  const app = createApp(App);
  setupStore(app);
  app.use(router);
  setupGlobCom(app);
  setupElementPlus(app);
  setupI18n(app);
  setupPermission(app);

  app.mount('#app');
};
setupAll();
