import { defineStore } from 'pinia';

interface IMenu {
  menuId: string; //菜单id
  parentId: string; //父级id 0为顶层id
  menuName: string; //菜单标题(中文)
  enMenuName: string; //菜单标题(英文)
  path: string; //对应路由名称
  component: string; //路由地址 #为菜单
  perms: string; //权限标识
  icon: string; //图标
  type: EMenuType; //0:菜单 1:组件
  orderNum: number; //排序
  createTime: string; //创建时间
  modifyTime: string; //更新时间 编辑时不传 后端根据事件戳更新
  hidden: string; //是否隐藏
  children: IMenu[];
}
enum EMenuType {
  Menu = 0,
  Component = 1
}

export interface IPiniaUserStore {
  session: string;
  userinfo: IPiniaUserInfo | undefined;
}
export interface IPiniaUserInfo {
  userId: string;
  username: string;
  fullName: string;
  avatar: string;
  createTime: string;
  deptId: string;
  description: string;
  email: string;
  idCard: string;
  jobNumber: string;
  lastLoginTime: string;
  mobile: string;
  modifyTime: string;
  name: string;
  orgId: string;
  phone: string;
  pinyinInitials: string;
  sex: string;
  source: string;
  status: string;
  syncId: string;
  roles: IUserRoles[];
  secretOutTime: boolean;
  loginName: string;
}
export interface IUserRoles {
  sort: string;
  roleName: string;
  roleId: string;
  remark: string;
  modifyTime: string;
  modifierId: string;
  modifier: string;
  creatorId: string;
  creator: string;
  createTime: string;
  menus: IMenu[];
}

export const useUserStore = defineStore('pinia-user', {
  state: (): IPiniaUserStore => ({
    session: '',
    userinfo: undefined
  }),
  persist: {
    enabled: true,
    strategies: [
      {
        storage: sessionStorage
      }
    ]
  },
  getters: {
    /**
     * 当前登录用户拥有的权限列表
     * @param state
     * @returns
     */
    havePermsList(state) {
      return (
        state.userinfo?.roles.reduce((total, current) => {
          return [...total, ...current.menus.map((m) => m.perms)];
        }, []) ?? []
      );
    },
    permsI18nList(state): { [key: string]: { menu_name: string; en_menu_name: string } } | object {
      let temp = {};
      state.userinfo?.roles.forEach((f) => {
        f.menus.forEach((fm) => {
          temp = {
            ...temp,
            ...{
              [fm.perms]: {
                menu_name: fm.menuName,
                en_menu_name: fm.enMenuName
              }
            }
          };
        });
      });
      return temp;
    }
  },
  actions: {
    setSession(session: string) {
      this.session = session;
    },
    setUserInfo(userinfo: IPiniaUserInfo | undefined) {
      this.userinfo = userinfo;
    }
  }
});
