<script setup lang="tsx">
  import { compareFileContentApi } from '@/api/comparisonResults';
  import { ElButton } from 'element-plus';
  import { ref } from 'vue';
  const detailInfo = ref('');
  const props = defineProps({
    currentRow: {
      type: Object,
      default: () => ({})
    }
  });

  // 新增：请求全部和差异的方法
  const getAll = async () => {
    const res = await compareFileContentApi({ flowId: props.currentRow?.flowId, que: 0 })
      .catch(() => {})
      .finally(() => {});
    if (res) {
      detailInfo.value = res.data;
    }
  };
  const getDifferent = async () => {
    const res = await compareFileContentApi({ flowId: props.currentRow?.flowId, que: 1 })
      .catch(() => {})
      .finally(() => {});
    if (res) {
      detailInfo.value = res.data;
    }
  };

  // 初始化默认请求全部
  const initFn = async () => {
    await getAll();
  };
  initFn();
</script>
<template>
  <div class="cpi_toolbar" style="text-align: right; padding-bottom: 16px">
    <el-button type="primary" @click="getAll" style="margin-right: 8px">全部</el-button>
    <el-button type="danger" @click="getDifferent">差异</el-button>
  </div>
  <div :innerHTML="detailInfo"></div>
</template>
<style>
  body {
    margin: 0;
    padding: 0;
  }
  html,
  body {
    height: 100%;
  }
  .comparison_space {
    padding: 0 10px;
  }
  .comparison_tab {
    font-size: 14px;
    font-family: Arial;
    width: 100%;
  }
  .comparison_tab .cp_title td span {
    padding: 0 0 0 15px;
  }
  .comparison_tab .cp_title {
    height: 45px;
  }
  .comparison_tab .cp_title td {
    border-bottom: 1px solid #e5e5e5;
  }
  .cp_line {
    background: url(../images/comparison_line.png) no-repeat center;
    background-repeat: repeat-y;
    height: auto;
  }
  .cp_frame {
    border-radius: 10px;
    border: 1px solid #e5e5e5;
  }
  .cp_frame table {
    padding: 5px 5px;
  }
  .cp_icon {
    background-image: url(../images/comparison_icon.png);
    width: 0px;
    height: 16px;
    margin: 0px 0px 0 0px;
  }

  .icon_pos {
    background-position: 0 0;
  }
  .icon_pos2 {
    background-position: 0 -16px;
  }
  .icon_pos3 {
    background-position: 0 -32px;
  }
  .cp_text {
    line-height: 30px;
    float: left;
    width: 40px;
    margin: 0 2px 0 0;
    text-align: left;
  }
  .cp_cn {
    white-space: normal;
    word-break: break-all;
    overflow: auto;
    float: left;
    line-height: 2;
  }
  .abnormal {
    background-color: rgba(251, 89, 78, 0.1);
    border: 1px solid rgba(251, 89, 78, 0.3);
    color: rgba(251, 89, 78);
  }
  .complete {
    background-color: rgba(12, 191, 71, 0.1);
    border: 1px solid rgba(12, 191, 71, 0.3);
    color: rgba(12, 191, 71);
  }
  .warning {
    background-color: rgba(255, 166, 2, 0.1);
    border: 1px solid rgba(255, 166, 2, 0.3);
    color: rgba(255, 166, 2);
  }
  .cpi_toolbar {
    padding: 10px 0;
    border-bottom: 1px solid #e5e5e5;
    width: 100%;
    text-align: right;
  }
  .cpi_toolbar input {
    border: 1px solid #13b2f6;
    color: #ffffff;
    font-size: 16px;
    border-radius: 2px;
    padding: 5px 10px;
    margin: 0 5px;
    cursor: pointer;
    background-color: #13b2f6;
  }
  .cpi_toolbar input:hover {
    background-color: #ffffff;
    color: #13b2f6;
  }
  .compare_font {
    font-size: 16px;
  }
  .cpi_td_w {
    width: 49.5%;
  }
</style>
