import request from '@/config/axios';

// 获取业务系统
export const getBusinessSystemlistApi = (data?: any) => {
  return request.post({ url: '/project/list', data });
};

// 获取待添加的业务系统
export const getPendingSystemlistApi = (data?: any) => {
  return request.post({ url: '/project/pendingSystemList', data });
};

export const addSystemApi = (data?: any) => {
  return request.post({ url: '/project/add', data });
};

export const removeSystemApi = (data?: any) => {
  return request.post({ url: '/project/remove', data });
};

// 获取设备列表
export const getEquipmentlistApi = (data?: any) => {
  return request.post({ url: '/sysmComputer/list', data });
};

// 获取待添加的设备列表
export const getPendingEquipmentlistApi = (data?: any) => {
  return request.post({ url: '/sysmComputer/pendingList', data });
};

// 保存设备
export const saveEquipmentApi = (data?: any) => {
  return request.post({ url: '/sysmComputer/add', data });
};

export const removeComputerApi = (data?: any) => {
  return request.post({ url: '/sysmComputer/remove', data });
};
