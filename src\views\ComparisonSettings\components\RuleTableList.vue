<script setup lang="tsx">
  import { useI18n } from '@/hooks/web/useI18n';
  import { Table } from '@/components/Table';
  import { useTable } from '@/hooks/web/useTable';
  import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas';
  import { ref, reactive, watch, onMounted, unref } from 'vue';
  import type { BatchOption } from '@/components/Table/src/types';
  import {
    ElTable,
    ElButton,
    ElDivider,
    ElMessage,
    ElPopconfirm,
    ElTag,
    ElDrawer
  } from 'element-plus';
  import {
    getRulelistApi,
    removeRuleApi,
    updateRuleStateApi,
    saveRuleApi,
    updateRuleApi
  } from '@/api/comparisonSettings';
  import ButtonGroup from '@/components/ButtonGroup/src/ButtonGroup.vue';
  import type { ButtonGroupSchema } from '@/components/ButtonGroup/src/types';
  import type { IQueryData, IRuleTable } from '@/api/comparisonSettings/types';
  import config from '@/config/axios/config';
  import AddRule from './AddRule.vue';

  const { result_code } = config;
  const { t } = useI18n();

  const props = defineProps({
    currentRow: {
      type: Object,
      default: () => {}
    }
  });

  const crudSchemas = reactive<CrudSchema[]>([
    {
      field: 'selection', //复选框
      table: {
        type: 'selection'
      }
    },
    {
      field: 'index',
      label: t('demo.index'),
      type: 'index'
    },
    {
      field: 'enabled',
      label: t('一致性比对.状态'),
      width: 90,
      table: {
        slots: {
          default: (data: any) => {
            return (
              <ElTag type={data.row.enabled === 1 ? 'danger' : 'success'}>
                {data.row.enabled === 1 ? t('common.禁用') : t('common.启用')}
              </ElTag>
            );
          }
        }
      }
    },
    {
      field: 'model',
      label: t('一致性比对.模式'),
      table: {
        slots: {
          default: (data: any) => {
            return (
              <span>
                {data.row.model === 0
                  ? t('一致性比对.比对')
                  : data.row.model === 1
                  ? t('一致性比对.同步')
                  : t('一致性比对.比对后同步')}
              </span>
            );
          }
        }
      }
    },
    {
      field: 'type',
      label: t('一致性比对.类型'),
      table: {
        slots: {
          default: (data: any) => {
            return (
              <span>
                {data.row.type === 0
                  ? t('一致性比对.目录')
                  : data.row.type === 1
                  ? t('一致性比对.文件')
                  : t('一致性比对.脚本')}
              </span>
            );
          }
        }
      }
    },
    {
      field: 'encode',
      label: t('一致性比对.字符集')
    },
    {
      field: 'sourcePath',
      label: t('一致性比对.源路径'),
      minWidth: 100
    },
    {
      field: 'path',
      label: t('一致性比对.目标路径'),
      minWidth: 100
    },
    {
      field: 'action',
      width: 170,
      label: t('common.handle'),
      table: {
        fixed: 'right',
        slots: {
          default: (data: any) => {
            return (
              <div>
                <ElButton
                  link
                  type="primary"
                  onClick={() => onActionFn(data.row, 'edit')}
                  v-hasPerm="updateRule"
                >
                  {t('common.edit')}
                </ElButton>
                <ElDivider direction="vertical" v-hasPerm="updateRule" />
                <ElButton link type="success" onClick={() => onActionFn(data.row, 'detail')}>
                  {t('common.detail')}
                </ElButton>
                <ElDivider direction="vertical" />
                <ElPopconfirm
                  title={t('一致性比对.是否确认启用')}
                  //@ts-ignore
                  placement="bottom-end"
                  onConfirm={() => updateStateFn(data.row, 0)}
                >
                  {{
                    reference: () => {
                      return (
                        <ElButton
                          link
                          type="primary"
                          style={{ 'margin-left': data.row.enabled === 1 && 0 }}
                          v-show={data.row.enabled === 1}
                          v-hasPerm="enableRule"
                        >
                          {t('common.启用')}
                        </ElButton>
                      );
                    }
                  }}
                </ElPopconfirm>
                <ElPopconfirm
                  title={t('一致性比对.是否确认禁用')}
                  //@ts-ignore
                  placement="bottom-end"
                  onConfirm={() => updateStateFn(data.row, 1)}
                >
                  {{
                    reference: () => {
                      return (
                        <ElButton
                          link
                          type="primary"
                          style={{ 'margin-left': data.row.enabled === 0 && 0 }}
                          v-show={data.row.enabled === 0}
                          v-hasPerm="disableRule"
                        >
                          {t('common.禁用')}
                        </ElButton>
                      );
                    }
                  }}
                </ElPopconfirm>
              </div>
            );
          }
        }
      }
    }
  ]);
  const { allSchemas } = useCrudSchemas(crudSchemas);
  //列表定义
  const queryData = reactive<IQueryData>({
    queryParam: { envcSystemComputerNodeId: props.currentRow.id },
    pageNum: 1,
    pageSize: 10
  });
  const selectedItemIds = ref<number[]>([]);
  const { tableRegister, tableState, tableMethods } = useTable({
    fetchDataApi: async () => {
      const { currentPage, pageSize } = tableState;
      queryData.pageNum = unref(currentPage);
      queryData.pageSize = unref(pageSize);
      const res = await getRulelistApi(queryData);
      return {
        list: res.data.list,
        total: res.data.total
      };
    }
  });
  const { loading, dataList, total, currentPage, pageSize } = tableState;
  const { getElTableExpose, refresh, getList } = tableMethods;

  let elTableExpose = ref<ComponentRef<typeof ElTable>>();
  const addruleRef = ref<InstanceType<typeof AddRule>>();
  const batchCount = ref<number>(0);
  const currentRowRule = ref<any>({});
  const actionType = ref<string>('add');
  const ruleTitle = ref('');
  const actionVisible = ref(false);
  const saveLoading = ref(false);

  /*按钮组定义*/
  const buttonSchema = ref<ButtonGroupSchema[]>([
    {
      type: 'primary',
      field: 'add',
      name: t('common.add'),
      perm: 'saveRule',
      option: () => {
        onActionFn(null, 'add');
      }
    }
  ]);
  /*批处理表格*/
  const batchOptions = ref<BatchOption[]>([
    {
      name: t('common.delete'),
      icon: 'mdi:trash-can-outline',
      title: t('common.delMessage'),
      perm: 'removeRule',
      option: () => {
        delData(null);
      }
    }
  ]);

  //删除
  const delData = async (row: IRuleTable | null) => {
    selectedItemIds.value = row
      ? [row.id]
      : elTableExpose.value!.getSelectionRows().map((v: IRuleTable) => v.id) || [];
    const res: any = await removeRuleApi(selectedItemIds.value).catch(() => {});
    if (res?.code === result_code) {
      ElMessage.success(t('common.delSuccess'));
      elTableExpose.value?.clearSelection();
      await getList();
    } else {
      ElMessage.error(res?.message);
    }
  };

  // 新增服务器
  const onActionFn = (row: IRuleTable | null, type: string) => {
    currentRowRule.value = row || {};
    actionType.value = type;
    ruleTitle.value =
      type === 'add'
        ? t('common.add') + t('一致性比对.规则')
        : type === 'edit'
        ? t('common.edit') + t('一致性比对.规则')
        : t('一致性比对.规则') + t('common.detail');
    actionVisible.value = true;
  };

  const saveFn = async () => {
    await addruleRef.value?.submit();
  };
  // 保存规则
  const submitRuleFn = async (ruleData: any) => {
    saveLoading.value = true;
    let params = {
      envcSystemComputerNodeId: props.currentRow.id,
      ...ruleData
    };
    if (actionType.value === 'edit') {
      params.id = currentRowRule.value.id;
    }
    const saveFunc =
      actionType.value === 'add'
        ? (data: any) => saveRuleApi(data)
        : (data: any) => updateRuleApi(data);
    const res: any = await saveFunc(params)
      .catch(() => {})
      .finally(() => {
        saveLoading.value = false;
      });
    if (res?.code === result_code) {
      actionType.value === 'add'
        ? ElMessage.success(t('common.addSuccess'))
        : ElMessage.success(t('common.editSuccess'));
      actionVisible.value = false;
      await getList();
    } else {
      ElMessage.error(res?.message);
    }
  };

  // 启用/禁用
  const updateStateFn = async (row: any, state: number) => {
    const params = { ids: [row.id], operationType: state };
    const res: any = await updateRuleStateApi(params).catch(() => {});
    if (res?.code === result_code) {
      state === 0
        ? ElMessage.success(t('common.启用成功'))
        : ElMessage.success(t('common.禁用成功'));
      await getList();
    }
  };

  onMounted(async () => {
    elTableExpose.value = await getElTableExpose();
    watch(
      () => elTableExpose.value?.getSelectionRows(),
      (val) => {
        batchCount.value = val.length;
      },
      { deep: true }
    );
  });
</script>

<template>
  <ButtonGroup :schema="buttonSchema" />
  <Table
    class="mt-1"
    v-model:pageSize="pageSize"
    v-model:currentPage="currentPage"
    :columns="allSchemas.tableColumns"
    :data="dataList"
    :loading="loading"
    :pagination="{ total: total }"
    @register="tableRegister"
    @refresh="refresh"
    showAction
    :batch-count="batchCount"
    :batch-options="batchOptions"
    :reserve-selection="true"
    row-key="id"
    :active-u-i-d="'ComparisonSettingsRuleTableList'"
    :emptyText="t('common.暂无数据')"
  />
  <!-- 添加规则 -->
  <ElDrawer
    v-if="actionVisible"
    v-model="actionVisible"
    :title="ruleTitle"
    destroy-on-close
    append-to-body
    size="40%"
  >
    <AddRule
      ref="addruleRef"
      :current-server="currentRow"
      :current-row="currentRowRule"
      :type="actionType"
      @save-rule="submitRuleFn"
    />
    <template #footer>
      <ElButton
        type="primary"
        :loading="saveLoading"
        v-if="actionType !== 'detail'"
        @click="saveFn"
      >
        {{ t('common.ok') }}
      </ElButton>
      <ElButton @click="actionVisible = false">{{ t('common.cancel') }}</ElButton>
    </template>
  </ElDrawer>
</template>
