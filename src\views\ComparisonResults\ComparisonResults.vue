<script setup lang="tsx">
  import { ContentWrap } from '@/components/ContentWrap';
  import { useI18n } from '@/hooks/web/useI18n';
  import { Search } from '@/components/Search';
  import { Table } from '@/components/Table';
  import { useTable } from '@/hooks/web/useTable';
  import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas';
  import { ref, reactive, unref, toRef, onMounted, watch } from 'vue';
  import ButtonGroup from '@/components/ButtonGroup/src/ButtonGroup.vue';
  import type { ButtonGroupSchema } from '@/components/ButtonGroup/src/types';
  import {
    getResultsListApi,
    exportOverviewApi,
    exportDetailApi,
    stopResultsApi,
    refreshResultsApi
  } from '@/api/comparisonResults';
  import type { IQueryData } from '@/api/comparisonResults/types';
  import {
    ElTable,
    ElButton,
    ElMessage,
    ElDivider,
    ElPopconfirm,
    ElTag,
    ElDrawer
  } from 'element-plus';
  import Detail from './components/Detail.vue';
  import config from '@/config/axios/config';
  const { t } = useI18n();
  const { result_code } = config;

  const crudSchemas = reactive<CrudSchema[]>([
    // {
    //   field: 'selection', //复选框
    //   table: {
    //     type: 'selection'
    //   }
    // },
    {
      field: 'index',
      label: t('demo.index'),
      type: 'index'
    },
    {
      field: 'result',
      label: t('一致性比对.比对结果'),
      width: 100,
      search: {
        component: 'Select',
        componentProps: {
          options: [
            { label: t('一致性比对.比对中'), value: -1 },
            { label: t('一致性比对.一致'), value: 0 },
            { label: t('一致性比对.不一致'), value: 1 },
            { label: t('一致性比对.比对失败'), value: 2 }
          ]
        }
      },
      align: 'center',
      table: {
        slots: {
          default: (data: any) => {
            return (
              <div>
                <ElTag class="running-color" v-show={data.row.result === -1}>
                  {t('一致性比对.比对中')}
                </ElTag>
                <ElTag type="success" v-show={data.row.result === 0}>
                  {t('一致性比对.一致')}
                </ElTag>
                <ElTag type="info" v-show={data.row.result === 1}>
                  {t('一致性比对.不一致')}
                </ElTag>
                <ElTag type="danger" v-show={data.row.result === 2}>
                  {t('一致性比对.比对失败')}
                </ElTag>
              </div>
            );
          }
        }
      }
    },
    {
      field: 'state',
      label: t('一致性比对.运行状态'),
      width: 100,
      align: 'center',
      table: {
        slots: {
          default: (data: any) => {
            return (
              <div>
                <ElTag class="running-color" v-show={data.row.state === 0}>
                  {t('一致性比对.运行中')}
                </ElTag>
                <ElTag type="success" v-show={data.row.state === 1}>
                  {t('一致性比对.完成')}
                </ElTag>
                <ElTag type="info" v-show={data.row.state === 2}>
                  {t('一致性比对.终止')}
                </ElTag>
              </div>
            );
          }
        }
      }
    },
    {
      field: 'id',
      label: t('一致性比对.批次号'),
      minWidth: 150
    },
    {
      field: 'triggerFrom',
      label: t('一致性比对.触发方式'),
      minWidth: 150
    },
    {
      field: 'from',
      label: t('一致性比对.触发方式'),
      minWidth: 100,
      table: {
        hidden: true
      },
      search: {
        component: 'Select',
        componentProps: {
          options: [
            { label: t('一致性比对.周期触发'), value: 1 },
            { label: t('一致性比对.手动触发'), value: 2 },
            { label: t('一致性比对.重试'), value: 3 }
          ]
        }
      }
    },
    {
      field: 'businessSystemName',
      label: t('一致性比对.业务系统'),
      minWidth: 145
    },
    {
      field: 'model',
      label: t('一致性比对.比对类型'),
      minWidth: 145,
      table: {
        slots: {
          default: (data: any) => {
            return (
              <span>
                {data.row.model === 0
                  ? t('一致性比对.比对')
                  : data.row.model === 1
                  ? t('一致性比对.同步')
                  : t('一致性比对.比对后同步')}
              </span>
            );
          }
        }
      }
    },
    {
      field: 'createTime',
      label: t('一致性比对.比对时间'),
      width: 170
    },
    {
      field: 'sourceComputerIp',
      label: t('一致性比对.源服务器'),
      minWidth: 120
    },
    {
      field: 'sourcePath',
      label: t('一致性比对.源路径'),
      minWidth: 150
    },
    {
      field: 'targetComputerIp',
      label: t('一致性比对.目标服务器'),
      minWidth: 120
    },
    {
      field: 'path',
      label: t('一致性比对.目标路径'),
      minWidth: 120
    },
    {
      field: 'elapsedTimeStr',
      label: t('一致性比对.耗时'),
      width: 170
    },
    {
      field: 'action',
      width: 150,
      label: t('common.handle'),
      table: {
        fixed: 'right',
        slots: {
          default: (data: any) => {
            return (
              <div>
                <ElButton
                  link
                  type="success"
                  onClick={() => onCompareDetailFn(data.row)}
                  v-hasPerm="runResultContentDetail"
                >
                  {t('一致性比对.比对详情')}
                </ElButton>
                <ElDivider direction="vertical" v-show={false} />
                <ElButton
                  link
                  type="primary"
                  onClick={() => onExportDetailFn(data.row)}
                  v-show={false}
                >
                  {t('一致性比对.导出详情')}
                </ElButton>
                <ElDivider
                  direction="vertical"
                  v-show={data.row.state === 0 || data.row.state === 2}
                />
                <ElPopconfirm title={t('common.确认终止')} onConfirm={() => stopFn(data.row)}>
                  {{
                    reference: () => {
                      return (
                        <ElButton
                          link
                          type="primary"
                          style={{ 'margin-left': data.row.state === 0 && 0 }}
                          v-show={data.row.state === 0}
                          v-hasPerm="stopFlowOfRule"
                        >
                          {t('common.终止')}
                        </ElButton>
                      );
                    }
                  }}
                </ElPopconfirm>
                <ElPopconfirm title={t('common.确认重试')} onConfirm={() => refreshFn(data.row)}>
                  {{
                    reference: () => {
                      return (
                        <ElButton
                          link
                          type="primary"
                          style={{ 'margin-left': data.row.state === 2 && 0 }}
                          v-show={data.row.state === 2}
                          v-hasPerm="retryFlowOfRule"
                        >
                          {t('common.重试')}
                        </ElButton>
                      );
                    }
                  }}
                </ElPopconfirm>
              </div>
            );
          }
        }
      }
    }
  ]);
  const { allSchemas } = useCrudSchemas(crudSchemas);
  //列表定义
  const queryData = reactive<IQueryData>({
    queryParam: {},
    pageNum: 1,
    pageSize: 10
  });
  const searchParams = toRef(queryData, 'queryParam');
  const { tableRegister, tableState, tableMethods } = useTable({
    fetchDataApi: async () => {
      const { currentPage, pageSize } = tableState;
      queryData.pageNum = unref(currentPage);
      queryData.pageSize = unref(pageSize);
      const res = await getResultsListApi(queryData);
      return {
        list: res.data.list,
        total: res.data.total
      };
    }
  });
  const { loading, dataList, total, currentPage, pageSize } = tableState;
  const { getList, getElTableExpose, refresh } = tableMethods;

  let elTableExpose = ref<ComponentRef<typeof ElTable>>();
  const batchCount = ref<number>(0);
  const currentRowInfo = ref<any>({});
  const actionVisible = ref(false);

  /*按钮组定义*/
  const buttonSchema = ref<ButtonGroupSchema[]>([
    {
      type: 'primary',
      field: 'export',
      name: t('一致性比对.导出概览'),
      hidden: true,
      option: () => {
        exportFn();
      }
    }
  ]);
  //查询
  const setSearchParams = (params: any) => {
    searchParams.value = params;
    getList();
  };

  // 比对详情
  const onCompareDetailFn = (row: any) => {
    currentRowInfo.value = row || {};
    actionVisible.value = true;
  };

  const formatDateMilliseconds = (milliseconds: any) => {
    let date = new Date(milliseconds);

    // 获取年、月、日、时、分、秒和毫秒
    let year = date.getFullYear();
    let month = (1 + date.getMonth()).toString().padStart(2, '0'); // 月份是从0开始的，所以需要+1
    let day = date.getDate().toString().padStart(2, '0');
    let hours = date.getHours().toString().padStart(2, '0');
    let minutes = date.getMinutes().toString().padStart(2, '0');
    let seconds = date.getSeconds().toString().padStart(2, '0');
    let millisecondsPart = date.getMilliseconds().toString().padStart(3, '0');

    // 拼接字符串
    return `${year}${month}${day}${hours}${minutes}${seconds}${millisecondsPart}`;
  };

  // 导出概览
  const exportFn = async () => {
    const now = Date.now();
    const nameStr = `导出概览_${formatDateMilliseconds(now)}`;
    const selectedRowsIds =
      elTableExpose.value?.getSelectionRows().map((item: any) => item.id) || [];
    const params = { ids: selectedRowsIds };
    await exportOverviewApi(nameStr, params)
      .then(async () => {})
      .catch((e) => {
        console.error(e);
      });
  };

  //导出详情
  const onExportDetailFn = async (row: any) => {
    const now = Date.now();
    const nameStr = `导出详情_${formatDateMilliseconds(now)}`;
    const params = { id: row.flowId };
    await exportDetailApi(nameStr, params)
      .then(async () => {})
      .catch((e) => {
        console.error(e);
      });
  };

  // 终止
  const stopFn = async (row: any) => {
    let params = { flowIds: [row.flowId] };
    const res = await stopResultsApi(params)
      .catch(() => {})
      .finally(() => {});
    if (res?.code === result_code) {
      ElMessage.success(t('common.终止成功'));
      getList();
    } else {
      ElMessage.error(res?.message);
    }
  };

  // 重试
  const refreshFn = async (row: any) => {
    let params = { flowId: row.flowId };
    const res = await refreshResultsApi(params)
      .catch(() => {})
      .finally(() => {});
    if (res?.code === result_code) {
      ElMessage.success(t('common.重试成功'));
      getList();
    } else {
      ElMessage.error(res?.message);
    }
  };

  onMounted(async () => {
    elTableExpose.value = await getElTableExpose();
    watch(
      () => elTableExpose.value?.getSelectionRows(),
      (val) => {
        batchCount.value = val.length;
      },
      { deep: true }
    );
  });
</script>

<template>
  <ContentWrap>
    <Search :schema="allSchemas.searchSchema" @search="setSearchParams" @reset="setSearchParams" />
    <ButtonGroup :schema="buttonSchema" />
    <Table
      class="Rd-table-cursor mt-1"
      v-model:pageSize="pageSize"
      v-model:currentPage="currentPage"
      :columns="allSchemas.tableColumns"
      :data="dataList"
      :loading="loading"
      :pagination="{ total: total }"
      @register="tableRegister"
      @refresh="refresh"
      showAction
      :batch-count="batchCount"
      :reserve-selection="true"
      row-key="flowId"
      :active-u-i-d="'ComparisonResults'"
      :emptyText="t('common.暂无数据')"
    />
    <!-- 比对详情 -->
    <ElDrawer
      v-if="actionVisible"
      v-model="actionVisible"
      :title="t('一致性比对.比对详情')"
      modal-class="Rd-comparison-detail-drawer"
      destroy-on-close
      append-to-body
      size="90%"
    >
      <Detail :current-row="currentRowInfo" />
      <template #footer>
        <ElButton @click="actionVisible = false">{{ t('common.cancel') }}</ElButton>
      </template>
    </ElDrawer>
  </ContentWrap>
</template>
<style scoped>
  .running-color {
    color: #409eff;
    background-color: #ecf5ff;
    border-color: #d9ecff;
  }
</style>
<style lang="less">
  .Rd-comparison-detail-drawer {
    .el-drawer__header {
      margin-bottom: 0;
    }
  }
</style>
