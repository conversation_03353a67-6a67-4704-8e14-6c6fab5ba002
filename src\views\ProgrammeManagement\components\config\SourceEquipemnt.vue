<script setup lang="tsx">
  import { useI18n } from '@/hooks/web/useI18n';
  import { Search } from '@/components/Search';
  import { Table } from '@/components/Table';
  import { useTable } from '@/hooks/web/useTable';
  import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas';
  import { ref, reactive, unref, toRef, onMounted, watch, nextTick } from 'vue';
  import { nodesComparingApi } from '@/api/programmeManagement';
  import { getPlanRelationSystemNodeListApi } from '@/api/programmeManagement';
  import type { IQueryData, ISystemConfiguration } from '@/api/comparisonSettings/types';
  import ButtonGroup from '@/components/ButtonGroup/src/ButtonGroup.vue';
  import type { ButtonGroupSchema } from '@/components/ButtonGroup/src/types';
  import { ElLoading, ElMessage, ElTable } from 'element-plus';
  import config from '@/config/axios/config';
  import { getCenterListApi } from '@/api/comparisonSettings';
  import { useSearch } from '@/hooks/web/useSearch';
  const { result_code } = config;
  const { t } = useI18n();
  const { searchRegister, searchMethods } = useSearch();
  const { setSchema } = searchMethods;

  const props = defineProps({
    currentSystemData: {
      type: Object,
      default: () => {}
    }
  });

  const crudSchemas = reactive<CrudSchema[]>([
    {
      field: 'selection', //复选框
      table: {
        type: 'selection'
      }
    },
    {
      field: 'index',
      label: t('demo.index'),
      type: 'index'
    },
    {
      field: 'sourceComputerIp',
      label: t('一致性比对.源设备IP'),
      minWidth: 120
    },
    {
      field: 'sourceCenterId',
      label: t('一致性比对.源中心'),
      hidden: true,
      search: {
        component: 'Select',
        componentProps: {
          filterable: true,
          options: []
        }
      }
    },
    {
      field: 'sourceCenterName',
      label: t('一致性比对.源中心'),
      minWidth: 120
    },
    {
      field: 'targetComputerIp',
      label: t('一致性比对.目标设备IP'),
      minWidth: 120
    },
    {
      field: 'targetCenterId',
      label: t('一致性比对.目标中心'),
      hidden: true,
      search: {
        component: 'Select',
        componentProps: {
          filterable: true,
          options: []
        }
      }
    },
    {
      field: 'targetCenterName',
      label: t('一致性比对.目标中心'),
      minWidth: 120
    }
  ]);
  const { allSchemas } = useCrudSchemas(crudSchemas);
  //列表定义
  const queryData = reactive<IQueryData>({
    queryParam: { businessSystemId: props.currentSystemData.businessSystemId },
    pageNum: 1,
    pageSize: 10
  });
  const searchParams = toRef(queryData, 'queryParam');
  const { tableRegister, tableState, tableMethods } = useTable({
    fetchDataApi: async () => {
      const { currentPage, pageSize } = tableState;
      queryData.pageNum = unref(currentPage);
      queryData.pageSize = unref(pageSize);
      const res = await getPlanRelationSystemNodeListApi(queryData);
      nextTick(() => {
        if (res.data.list.length > 0) {
          rowClickFn(res.data.list[0]);
          setCurrent(res.data.list[0]);
        } else {
          currentRow.value = {};
          setCurrent();
        }
      });
      return {
        list: res.data.list,
        total: res.data.total
      };
    }
  });
  const { loading, dataList, total, currentPage, pageSize } = tableState;
  const { getList, getElTableExpose, refresh } = tableMethods;

  const getCenterListFn = async () => {
    const res = await getCenterListApi({});
    centerDataList.value = (res?.data || []).map((item: any) => {
      return { label: item.name, value: item.id };
    });
    setSchema([
      {
        field: 'sourceCenterId',
        path: 'componentProps.options',
        value: centerDataList.value
      },
      {
        field: 'targetCenterId',
        path: 'componentProps.options',
        value: centerDataList.value
      }
    ]);
  };

  /*按钮组定义*/
  const buttonSchema = ref<ButtonGroupSchema[]>([
    {
      type: 'primary',
      field: 'comparing',
      name: t('一致性比对.开始比对'),
      perm: 'startImmeSourceTargetCompare',
      option: () => {
        startComparingFn();
      }
    }
  ]);

  let elTableExpose = ref<ComponentRef<typeof ElTable>>();
  const currentRow = ref<any>({});
  const batchCount = ref<number>(0);
  const centerDataList = ref<any[]>([]);

  getCenterListFn();

  //查询
  const setSearchParams = (params: any) => {
    searchParams.value = { ...params, businessSystemId: props.currentSystemData.businessSystemId };
    getList();
  };

  const startComparingFn = async () => {
    const selectedRows = elTableExpose.value?.getSelectionRows();
    if (selectedRows?.length == 0) {
      ElMessage.warning(t('一致性比对.请选择设备'));
      return;
    }
    const pageLoading = ElLoading.service({
      lock: true,
      text: '比对中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    const params = {
      nodeIds: selectedRows.map((item: any) => item.id)
    };
    const res = await nodesComparingApi(params)
      .catch(() => {})
      .finally(() => {
        pageLoading.close();
      });
    if (res?.code === result_code) {
      ElMessage.success(t('一致性比对.比对成功'));
      elTableExpose.value?.clearSelection();
    } else {
      ElMessage.error(res?.message);
    }
  };

  const emit = defineEmits(['rowClick']); // 定义一个自定义事件，用于传递参数给父组件
  const rowClickFn = (row: ISystemConfiguration) => {
    currentRow.value = row;
    emit('rowClick', row); // 触发父组件的事件并传递参数
  };

  const setCurrent = (row?: any) => {
    elTableExpose.value!.setCurrentRow(row);
  };

  onMounted(async () => {
    elTableExpose.value = await getElTableExpose();
    watch(
      () => elTableExpose.value?.getSelectionRows(),
      (val) => {
        batchCount.value = val.length;
      },
      { deep: true }
    );
  });
</script>

<template>
  <Search
    :schema="allSchemas.searchSchema"
    @search="setSearchParams"
    @reset="setSearchParams"
    @register="searchRegister"
  />
  <ButtonGroup :schema="buttonSchema" />
  <Table
    class="mt-1 Rd-source-equipment-table"
    v-model:pageSize="pageSize"
    v-model:currentPage="currentPage"
    :columns="allSchemas.tableColumns"
    :data="dataList"
    :loading="loading"
    :pagination="{ total: total }"
    @register="tableRegister"
    @refresh="refresh"
    @row-click="rowClickFn"
    highlight-current-row
    :batch-count="batchCount"
    :reserve-selection="true"
    showAction
    row-key="id"
    :active-u-i-d="'ProgrammeManagementSourceEquipemnt'"
    :emptyText="t('common.暂无数据')"
  />
</template>
<style lang="less">
  .Rd-source-equipment-table {
    .el-table__body {
      .el-table__row {
        cursor: pointer;
      }
    }
  }
</style>
