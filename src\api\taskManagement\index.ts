import request from '@/config/axios';

export const getTaskManagementListApi = (data?: any) => {
  return request.post({ url: '/task/list', data });
};

export const removeTaskManagementApi = (data?: any) => {
  return request.post({ url: '/task/removeScheduleJob', data });
};

export const updateStateApi = (data?: any) => {
  return request.post({ url: '/task/operate', data });
};

export const updateCronApi = (data?: any) => {
  return request.post({ url: '/task/updateCron', data });
};
