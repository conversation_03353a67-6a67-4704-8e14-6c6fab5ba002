.no-vue3-cron-div .language {
  position: absolute;
  right: 25px;
  z-index: 1;
}
.no-vue3-cron-div .el-tabs {
  box-shadow: none;
}
.no-vue3-cron-div .tabBody {
  overflow: auto;
}
.no-vue3-cron-div .tabBody .el-row {
  margin: 20px 0;
}
.no-vue3-cron-div .tabBody .el-row .long .el-select {
  width: 200px;
}
.no-vue3-cron-div .tabBody .el-row .el-input-number {
  width: 120px;
}
.no-vue3-cron-div .myScroller::-webkit-scrollbar {
  width: 5px;
  height: 1px;
}
.no-vue3-cron-div .myScroller::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #87ceeb;
  background-image: -webkit-linear-gradient(
    45deg,
    hsla(0, 0%, 100%, 0.2) 25%,
    transparent 0,
    transparent 50%,
    hsla(0, 0%, 100%, 0.2) 0,
    hsla(0, 0%, 100%, 0.2) 75%,
    transparent 0,
    transparent
  );
}
.no-vue3-cron-div .myScroller::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #ededed;
  border-radius: 10px;
}
.no-vue3-cron-div .bottom {
  width: 100%;
  margin-top: 5px;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.no-vue3-cron-div .bottom .value {
  float: left;
  font-size: 14px;
  vertical-align: middle;
}
.no-vue3-cron-div .bottom .value span:first-child {
  color: red;
}
