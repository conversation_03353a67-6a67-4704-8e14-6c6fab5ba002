import request from '@/config/axios';

export const getResultsListApi = (data?: any) => {
  return request.post({ url: '/result/list', data });
};

// 导出概览
export const exportOverviewApi = async (name?: string, data?: any) => {
  const res = await request.post({
    url: '/project/exportOverview',
    data,
    responseType: 'blob'
  });
  const filename = `${name || Date.now()}.xlsx`;
  const blob = new Blob([res.data], {
    type: 'application/octet-stream'
  });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = url;
  link.setAttribute('download', filename);
  document.body.appendChild(link);
  link.click();
  // 清理工作
  document.body.removeChild(link);
};

// 导出详情
export const exportDetailApi = async (name?: string, data?: any) => {
  const res = await request.post({
    url: '/project/exportDetail',
    data,
    responseType: 'blob'
  });
  const filename = `${name || Date.now()}.xlsx`;
  const blob = new Blob([res.data], {
    type: 'application/octet-stream'
  });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = url;
  link.setAttribute('download', filename);
  document.body.appendChild(link);
  link.click();
  // 清理工作
  document.body.removeChild(link);
};

// 终止
export const stopResultsApi = (data?: any) => {
  return request.post({ url: '/operateFlow/stop', data });
};

export const refreshResultsApi = (params?: any) => {
  return request.post({ url: '/operateFlow/retry', params });
};

export const getComparisonContentApi = (params?: any) => {
  return request.post({ url: '/result/content', params });
};

export const compareFileContentApi = (params?: any) => {
  return request.post({ url: '/resultDetail/compareFileContent', params });
};
