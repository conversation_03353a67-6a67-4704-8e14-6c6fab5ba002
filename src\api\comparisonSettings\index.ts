import request from '@/config/axios';

export const removeServerApi = (data?: any) => {
  return request.post({ url: '/node/unbindComputer', data });
};

// 获取服务器列表
export const getServerListApi = (data?: any) => {
  return request.post({ url: '/node/nodeList', data });
};

// 获取中心列表
export const getCenterListApi = (data?: any) => {
  return request.post({ url: '/center/list', data });
};

// 获取源服务器列表
export const getSourceServerIpListApi = (data?: any) => {
  return request.post({ url: '/node/computerSourceList', data });
};

// 获取目标服务器
export const getPendingTargetServerlistApi = (data?: any) => {
  return request.post({ url: '/node/computerListPage', data });
};

// 添加绑定服务器
export const addBindServerApi = (data?: any) => {
  return request.post({ url: '/node/bind', data });
};

export const getRulelistApi = (data?: any) => {
  return request.post({ url: '/relation/list', data });
};

// 获取字符集列表
export const getCharactersetApi = (data?: any) => {
  return request.post({ url: '/charset/list', data });
};

export const removeRuleApi = (data?: any) => {
  return request.post({ url: '/relation/remove', data });
};

export const updateRuleStateApi = (data?: any) => {
  return request.post({ url: '/relation/changeEnabled', data });
};

export const saveRuleApi = (data?: any) => {
  return request.post({ url: '/relation/save', data });
};

export const updateRuleApi = (data?: any) => {
  return request.post({ url: '/relation/update', data });
};
