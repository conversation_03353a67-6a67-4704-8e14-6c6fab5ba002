<script setup lang="tsx">
  import { useI18n } from '@/hooks/web/useI18n';
  import { Search } from '@/components/Search';
  import { Table } from '@/components/Table';
  import { useTable } from '@/hooks/web/useTable';
  import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas';
  import { ref, reactive, unref, watch, onMounted, toRef } from 'vue';
  import type { BatchOption } from '@/components/Table/src/types';
  import { ElTable, ElButton, ElDrawer, ElMessage } from 'element-plus';
  import {
    getServerListApi,
    getCenterListApi,
    addBindServerApi,
    removeServerApi
  } from '@/api/comparisonSettings';
  import ButtonGroup from '@/components/ButtonGroup/src/ButtonGroup.vue';
  import type { ButtonGroupSchema } from '@/components/ButtonGroup/src/types';
  import type { IQueryData, IServerTable } from '@/api/comparisonSettings/types';
  import config from '@/config/axios/config';
  import AddServer from './AddServer.vue';
  import RuleTableList from './RuleTableList.vue';
  import { useSearch } from '@/hooks/web/useSearch';

  const { result_code } = config;
  const { t } = useI18n();
  const { searchRegister, searchMethods } = useSearch();
  const { setValues, setSchema } = searchMethods;

  const props = defineProps({
    currentRowSystem: {
      type: Object,
      default: () => ({})
    }
  });

  const crudSchemas = reactive<CrudSchema[]>([
    {
      field: 'selection', //复选框
      table: {
        type: 'selection'
      }
    },
    {
      field: 'index',
      label: t('demo.index'),
      type: 'index',
      detail: {
        hidden: true
      }
    },
    {
      field: 'sourceComputerIp',
      label: t('一致性比对.源服务器IP'),
      minWidth: 120
    },
    {
      field: 'sourceCenterName',
      label: t('一致性比对.源中心')
    },
    {
      field: 'sourceCenterId',
      label: t('一致性比对.源中心'),
      hidden: true,
      search: {
        component: 'Select',
        componentProps: {
          filterable: true,
          options: []
        }
      }
    },
    {
      field: 'targetComputerIp',
      label: t('一致性比对.目标服务器IP'),
      minWidth: 150
    },
    {
      field: 'targetCenterName',
      label: t('一致性比对.目标中心'),
      minWidth: 100
    },
    {
      field: 'targetCenterId',
      label: t('一致性比对.目标中心'),
      hidden: true,
      search: {
        component: 'Select',
        componentProps: {
          filterable: true,
          options: []
        }
      }
    },
    {
      field: 'action',
      width: 100,
      label: t('common.handle'),
      table: {
        fixed: 'right',
        slots: {
          default: (data: any) => {
            return (
              <ElButton
                link
                type="primary"
                onClick={() => ruleListFn(data.row)}
                v-hasPerm="ruleConfiguration"
              >
                {t('一致性比对.规则')}
              </ElButton>
            );
          }
        }
      }
    }
  ]);
  const { allSchemas } = useCrudSchemas(crudSchemas);
  //列表定义
  const selectedItemIds = ref<number[]>([]);
  const queryData = reactive<IQueryData>({
    queryParam: {},
    pageNum: 1,
    pageSize: 10
  });
  const searchParams = toRef(queryData, 'queryParam');
  const { tableRegister, tableState, tableMethods } = useTable({
    immediate: false,
    fetchDataApi: async () => {
      const { currentPage, pageSize } = tableState;
      queryData.pageNum = unref(currentPage);
      queryData.pageSize = unref(pageSize);
      const res = await getServerListApi(queryData);
      return {
        list: res.data.list,
        total: res.data.total
      };
    },
    fetchDelApi: async () => {
      const res = await removeServerApi(selectedItemIds.value);
      return !!res;
    }
  });
  const { loading, dataList, total, currentPage, pageSize } = tableState;
  const { getList, getElTableExpose, refresh, delList } = tableMethods;

  //查询
  const setSearchParams = (params: any) => {
    searchParams.value = { ...params, businessSystemId: props.currentRowSystem?.businessSystemId };
    getList();
  };

  const getCenterListFn = async () => {
    const res = await getCenterListApi({});
    centerDataList.value = (res?.data || []).map((item: any) => {
      return { label: item.name, value: item.id };
    });
    setSchema([
      {
        field: 'sourceCenterId',
        path: 'componentProps.options',
        value: centerDataList.value
      },
      {
        field: 'targetCenterId',
        path: 'componentProps.options',
        value: centerDataList.value
      }
    ]);
  };

  // 初始化数据
  const initialDataFn = () => {
    getCenterListFn();
    setValues({
      sourceCenterId: null
    });
    setValues({
      targetCenterId: null
    });
    queryData.queryParam = {
      businessSystemId: props.currentRowSystem?.businessSystemId
    };
    getList();
  };

  const actionVisible = ref(false);
  const batchCount = ref<number>(0);
  const saveLoading = ref(false);
  const addserverRef = ref<InstanceType<typeof AddServer>>();
  const currentRowServer = ref<IServerTable>();
  const ruleVisible = ref(false);
  const centerDataList = ref<any[]>([]);

  //删除
  const delLoading = ref(false);
  let elTableExpose = ref<ComponentRef<typeof ElTable>>();
  const delData = async (row: IServerTable | null) => {
    selectedItemIds.value = row
      ? [row.id]
      : elTableExpose.value!.getSelectionRows().map((v: IServerTable) => v.id) || [];
    delLoading.value = true;
    await delList(unref(selectedItemIds).length).finally(() => {
      delLoading.value = false;
    });
  };

  /*按钮组定义*/
  const buttonSchema = ref<ButtonGroupSchema[]>([
    {
      type: 'primary',
      field: 'add',
      name: t('common.add'),
      perm: 'saveSourceComputer',
      option: () => {
        addActionFn();
      }
    }
  ]);
  /*批处理表格*/
  const batchOptions = ref<BatchOption[]>([
    {
      name: t('common.delete'),
      icon: 'mdi:trash-can-outline',
      title: t('common.delMessage'),
      perm: 'removeSourceComputer',
      option: () => {
        delData(null);
      }
    }
  ]);

  // 新增服务器
  const addActionFn = () => {
    if (Object.keys(props.currentRowSystem).length === 0) {
      ElMessage.warning(t('一致性比对.请选择业务系统'));
      return;
    }
    actionVisible.value = true;
  };

  /*保存表单*/
  const saveFn = async () => {
    addserverRef.value!.submit(); // 调用子组件的方法
  };
  const submitSaveServer = async (submitDatas: any) => {
    let params = { ...submitDatas };
    saveLoading.value = true;
    const res: any = await addBindServerApi(params)
      .catch(() => {})
      .finally(() => {
        saveLoading.value = false;
      });
    if (res && res.code == result_code) {
      ElMessage.success(t('common.addSuccess'));
      actionVisible.value = false;
      currentPage.value = 1;
      await getList();
    } else {
      ElMessage.error(res?.message);
    }
  };

  // 规则列表
  const ruleListFn = (row: any) => {
    currentRowServer.value = row;
    ruleVisible.value = true;
  };

  onMounted(async () => {
    elTableExpose.value = await getElTableExpose();
    watch(
      () => elTableExpose.value?.getSelectionRows(),
      (val) => {
        batchCount.value = val.length;
      },
      { deep: true }
    );
  });

  watch(
    () => props.currentRowSystem,
    (val) => {
      if (Object.keys(val).length === 0) {
        dataList.value = [];
        return;
      }
      initialDataFn();
    },
    { deep: true, immediate: true }
  );
</script>

<template>
  <Search
    :schema="allSchemas.searchSchema"
    @search="setSearchParams"
    @reset="setSearchParams"
    @register="searchRegister"
  />
  <ButtonGroup :schema="buttonSchema" />
  <Table
    class="mt-1"
    v-model:pageSize="pageSize"
    v-model:currentPage="currentPage"
    :columns="allSchemas.tableColumns"
    :data="dataList"
    :loading="loading"
    :pagination="{ total: total }"
    @register="tableRegister"
    @refresh="refresh"
    showAction
    :batch-count="batchCount"
    :batch-options="batchOptions"
    :reserve-selection="true"
    row-key="id"
    :active-u-i-d="'ComparisonSettingsServer'"
    :emptyText="t('common.暂无数据')"
  />
  <!-- 添加服务器 -->
  <ElDrawer
    v-model="actionVisible"
    :title="t('common.add') + t('一致性比对.服务器')"
    destroy-on-close
    size="40%"
  >
    <AddServer
      ref="addserverRef"
      :business-system-id="currentRowSystem?.businessSystemId"
      @save-server="submitSaveServer"
    />
    <template #footer>
      <ElButton type="primary" :loading="saveLoading" @click="saveFn">
        {{ t('common.ok') }}
      </ElButton>
      <ElButton @click="actionVisible = false">{{ t('common.cancel') }}</ElButton>
    </template>
  </ElDrawer>
  <!-- 规则列表 -->
  <ElDrawer
    v-if="ruleVisible"
    v-model="ruleVisible"
    :title="t('一致性比对.规则')"
    destroy-on-close
    append-to-body
    size="70%"
  >
    <RuleTableList :current-row="currentRowServer" />
  </ElDrawer>
</template>
