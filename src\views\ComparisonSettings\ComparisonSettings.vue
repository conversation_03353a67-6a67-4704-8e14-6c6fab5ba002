<script setup lang="tsx">
  import { ref } from 'vue';
  import { ElCard } from 'element-plus';
  import BusinessSystemTable from './components/BusinessSystemTable.vue';
  import ServerTable from './components/ServerTable.vue';
  import { useI18n } from '@/hooks/web/useI18n';
  const { t } = useI18n();

  const currentSystem = ref<any>({});

  const rowSystemClick = (row: any) => {
    currentSystem.value = row;
  };
</script>
<template>
  <div class="flex gap-2">
    <el-card style="width: 45%">
      <template #header>{{ t('一致性比对.业务系统') }}</template>
      <BusinessSystemTable @row-click="rowSystemClick" />
    </el-card>
    <el-card style="width: 55%">
      <template #header>{{ t('一致性比对.服务器') }}</template>
      <ServerTable :current-row-system="currentSystem" />
    </el-card>
  </div>
</template>
