<script setup lang="tsx">
  import { reactive, ref } from 'vue';
  import {
    ElForm,
    ElFormItem,
    ElInput,
    ElTooltip,
    ElDialog,
    ElButton,
    FormInstance
  } from 'element-plus';
  import { Setting } from '@element-plus/icons-vue';
  import { NoVue3Cron } from '@/components/NoVue3Cron';
  import { useI18n } from '@/hooks/web/useI18n';
  const { t } = useI18n();

  const props = defineProps({
    currentRow: {
      type: Object,
      default: () => ({})
    }
  });

  const formRef = ref<FormInstance>();
  const ruleForm = reactive({
    cron: props.currentRow.cron || ''
  });
  const rules = reactive({
    cron: [{ required: true, message: t('一致性比对.请选择周期表达式') }]
  });

  const state = reactive({
    cron: ''
  });
  const cronVisible = ref<boolean>(false); // 控制cron组件的显示与隐藏

  // 配置cron表达式
  const setCronFn = () => {
    cronVisible.value = true;
  };

  const changeCron = (val: any) => {
    if (typeof val !== 'string') return false;
    ruleForm.cron = val;
  };

  const closeCronFn = () => {
    cronVisible.value = false;
  };

  const emit = defineEmits(['savePeriodConfig']);
  const submit = async () => {
    let valid = await formRef.value?.validate().catch(() => {});
    if (valid) {
      let params: any = {
        ...ruleForm,
        id: props.currentRow.id
      };
      emit('savePeriodConfig', params);
    }
  };

  defineExpose({ submit });
</script>
<template>
  <div>
    <el-form
      ref="formRef"
      :model="ruleForm"
      :rules="rules"
      :inline="false"
      label-width="auto"
      @submit.prevent
    >
      <el-form-item :label="t('一致性比对.周期表达式')" style="width: 100%" prop="cron">
        <el-input v-model="ruleForm.cron" :placeholder="t('一致性比对.请选择周期表达式')" readonly>
          <template #append>
            <el-tooltip effect="dark" :content="t('一致性比对.选择周期')" placement="bottom-end">
              <el-button :icon="Setting" @click="setCronFn" />
            </el-tooltip>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <!-- 周期表达式 -->
    <el-dialog v-if="cronVisible" v-model="cronVisible" width="40%" append-to-body destroy-on-close>
      <NoVue3Cron
        :cron-value="state.cron"
        @change="changeCron"
        @close="closeCronFn"
        max-height="228px"
        i18n="cn"
      />
    </el-dialog>
  </div>
</template>
