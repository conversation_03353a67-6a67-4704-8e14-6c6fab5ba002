<script setup lang="tsx">
  import { ContentWrap } from '@/components/ContentWrap';
  import { useI18n } from '@/hooks/web/useI18n';
  import { Search } from '@/components/Search';
  import { Table } from '@/components/Table';
  import type { BatchOption } from '@/components/Table/src/types';
  import { useTable } from '@/hooks/web/useTable';
  import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas';
  import { ref, reactive, unref, toRef, onMounted, watch } from 'vue';
  import ButtonGroup from '@/components/ButtonGroup/src/ButtonGroup.vue';
  import type { ButtonGroupSchema } from '@/components/ButtonGroup/src/types';
  import {
    getTaskManagementListApi,
    removeTaskManagementApi,
    updateStateApi,
    updateCronApi
  } from '@/api/taskManagement';
  import type { IQueryData, ITaskManagementTable } from '@/api/taskManagement/types';
  import { ElTable, El<PERSON>utton, ElM<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ElTag } from 'element-plus';
  import PeriodConfig from './components/PeriodConfig.vue';
  import config from '@/config/axios/config';
  const { t } = useI18n();
  const { result_code } = config;

  const crudSchemas = reactive<CrudSchema[]>([
    {
      field: 'selection', //复选框
      table: {
        type: 'selection'
      }
    },
    {
      field: 'index',
      label: t('demo.index'),
      type: 'index'
    },
    {
      field: 'state',
      label: t('一致性比对.状态'),
      width: 100,
      table: {
        slots: {
          default: (data: any) => {
            return (
              <span>
                <ElTag type="success" v-show={data.row.state === 0}>
                  {t('common.启动')}
                </ElTag>
                <ElTag type="danger" v-show={data.row.state === 1}>
                  {t('common.终止')}
                </ElTag>
              </span>
            );
          }
        }
      }
    },
    {
      field: 'planName',
      label: t('一致性比对.方案名称'),
      minWidth: 145,
      search: {
        component: 'Input'
      }
    },
    {
      field: 'planDesc',
      label: t('一致性比对.方案描述'),
      minWidth: 145
    },
    {
      field: 'sourceCenterName',
      label: t('一致性比对.源中心'),
      minWidth: 120
    },
    {
      field: 'targetCenterName',
      label: t('一致性比对.目标中心'),
      minWidth: 120
    },
    {
      field: 'cron',
      label: t('一致性比对.周期表达式'),
      width: 170
    },
    {
      field: 'creatorName',
      label: t('一致性比对.创建人'),
      minWidth: 120
    },
    {
      field: 'createTime',
      label: t('一致性比对.创建时间'),
      width: 170
    },
    {
      field: 'action',
      width: 100,
      label: t('common.handle'),
      table: {
        fixed: 'right',
        slots: {
          default: (data: any) => {
            return (
              <div>
                <ElButton
                  link
                  type="primary"
                  onClick={() => onPeriodConfigFn(data.row)}
                  v-hasPerm="updateCronOfScheduleTask"
                >
                  {t('一致性比对.周期配置')}
                </ElButton>
              </div>
            );
          }
        }
      }
    }
  ]);
  const { allSchemas } = useCrudSchemas(crudSchemas);
  //列表定义
  const queryData = reactive<IQueryData>({
    queryParam: {},
    pageNum: 1,
    pageSize: 10
  });
  const searchParams = toRef(queryData, 'queryParam');
  const selectedItemIds = ref<number[]>([]);
  const { tableRegister, tableState, tableMethods } = useTable({
    fetchDataApi: async () => {
      const { currentPage, pageSize } = tableState;
      queryData.pageNum = unref(currentPage);
      queryData.pageSize = unref(pageSize);
      const res = await getTaskManagementListApi(queryData);
      return {
        list: res.data.list,
        total: res.data.total
      };
    },
    fetchDelApi: async () => {
      const res = await removeTaskManagementApi(selectedItemIds.value);
      return !!res;
    }
  });
  const { loading, dataList, total, currentPage, pageSize } = tableState;
  const { getList, getElTableExpose, refresh, delList } = tableMethods;

  let elTableExpose = ref<ComponentRef<typeof ElTable>>();
  let periodRef = ref<ComponentRef<typeof PeriodConfig>>();
  const batchCount = ref<number>(0);
  const currentRowInfo = ref<any>({});
  const actionVisible = ref(false);
  const saveLoading = ref(false);

  /*按钮组定义*/
  const buttonSchema = ref<ButtonGroupSchema[]>([
    {
      type: 'primary',
      field: 'startup',
      name: t('common.启动'),
      perm: 'startScheduleTask',
      option: () => {
        updateStateFn(0);
      }
    },
    {
      type: 'primary',
      field: 'stop',
      name: t('common.终止'),
      perm: 'stopScheduleTask',
      option: () => {
        updateStateFn(1);
      }
    }
  ]);
  /*批处理表格*/
  const batchOptions = ref<BatchOption[]>([
    {
      name: t('common.delete'),
      icon: 'mdi:trash-can-outline',
      title: t('common.delMessage'),
      perm: 'removeScheduleTask',
      option: () => {
        delData(null);
      }
    }
  ]);

  //查询
  const setSearchParams = (params: any) => {
    searchParams.value = params;
    getList();
  };

  //删除
  const delLoading = ref(false);
  const delData = async (row: ITaskManagementTable | null) => {
    selectedItemIds.value = row
      ? [row.id]
      : elTableExpose.value!.getSelectionRows().map((v: ITaskManagementTable) => v.id) || [];
    delLoading.value = true;
    await delList(unref(selectedItemIds).length).finally(() => {
      delLoading.value = false;
    });
  };

  //周期配置
  const onPeriodConfigFn = (row: any) => {
    currentRowInfo.value = row || {};
    actionVisible.value = true;
  };

  const saveFn = async () => {
    periodRef.value!.submit();
  };

  const submitPeriodConfigFn = async (submitDatas: any) => {
    saveLoading.value = true;
    let params = {
      ...submitDatas
    };
    const res: any = await updateCronApi(params).finally(() => {
      saveLoading.value = false;
    });
    if (res?.code === result_code) {
      ElMessage.success(t('common.updateSuccess'));
      actionVisible.value = false;
      getList();
    } else {
      ElMessage.error(res?.message);
    }
  };

  // 启动、终止
  const updateStateFn = async (state: number) => {
    const selectedRows = elTableExpose.value?.getSelectionRows();
    if (selectedRows?.length == 0) {
      ElMessage.warning(t('一致性比对.请选择任务'));
      return;
    }
    let hasState = false;
    hasState = selectedRows.some((item: ITaskManagementTable) => item.state === state);
    if (hasState) {
      ElMessage({
        message: t('一致性比对.状态已有重新选择', { state: state === 0 ? '启动' : '终止' }),
        type: 'warning',
        showClose: true
      });
      return;
    }
    const selectedItemIds = selectedRows.map((v: ITaskManagementTable) => v.id) || [];
    let params = {
      taskIdList: selectedItemIds,
      operateType: state
    };
    const res = await updateStateApi(params)
      .catch(() => {})
      .finally(() => {});
    if (res?.code === result_code) {
      state === 0
        ? ElMessage.success(t('common.启动成功'))
        : ElMessage.success(t('common.终止成功'));
      getList();
    } else {
      ElMessage.error(res?.message);
    }
  };

  onMounted(async () => {
    elTableExpose.value = await getElTableExpose();
    watch(
      () => elTableExpose.value?.getSelectionRows(),
      (val) => {
        batchCount.value = val.length;
      },
      { deep: true }
    );
  });
</script>

<template>
  <ContentWrap>
    <Search :schema="allSchemas.searchSchema" @search="setSearchParams" @reset="setSearchParams" />
    <ButtonGroup :schema="buttonSchema" />
    <Table
      class="Rd-table-cursor mt-1"
      v-model:pageSize="pageSize"
      v-model:currentPage="currentPage"
      :columns="allSchemas.tableColumns"
      :data="dataList"
      :loading="loading"
      :pagination="{ total: total }"
      @register="tableRegister"
      @refresh="refresh"
      showAction
      :batch-count="batchCount"
      :batch-options="batchOptions"
      :reserve-selection="true"
      row-key="id"
      :active-u-i-d="'TaskManagement'"
      :emptyText="t('common.暂无数据')"
    />
    <!-- 周期配置 -->
    <ElDrawer
      v-model="actionVisible"
      :title="t('一致性比对.周期配置')"
      destroy-on-close
      append-to-body
      size="30%"
    >
      <PeriodConfig
        ref="periodRef"
        :current-row="currentRowInfo"
        @save-period-config="submitPeriodConfigFn"
      />
      <template #footer>
        <ElButton type="primary" :loading="saveLoading" @click="saveFn">
          {{ t('common.ok') }}
        </ElButton>
        <ElButton @click="actionVisible = false">{{ t('common.cancel') }}</ElButton>
      </template>
    </ElDrawer>
  </ContentWrap>
</template>
