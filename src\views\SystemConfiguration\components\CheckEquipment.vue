<script setup lang="tsx">
  import { useI18n } from '@/hooks/web/useI18n';
  import { Search } from '@/components/Search';
  import { Table } from '@/components/Table';
  import { useTable } from '@/hooks/web/useTable';
  import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas';
  import { ref, reactive, unref, toRef, onMounted, watch } from 'vue';
  import { getPendingEquipmentlistApi } from '@/api/systemConfiguration';
  import type { IQueryData } from '@/api/systemConfiguration/types';
  import { ElMessage, ElTable } from 'element-plus';
  const { t } = useI18n();

  const props = defineProps({
    businessSystemId: {
      type: String,
      default: () => ''
    }
  });

  const crudSchemas = reactive<CrudSchema[]>([
    {
      field: 'selection', //复选框
      table: {
        type: 'selection'
      }
    },
    {
      field: 'index',
      label: t('demo.index'),
      type: 'index',
      detail: {
        hidden: true
      }
    },
    {
      field: 'computerName',
      label: t('一致性比对.设备名称'),
      search: {
        component: 'Input'
      }
    },
    {
      field: 'computerIp',
      label: t('一致性比对.设备IP')
    },
    {
      field: 'centerName',
      label: t('一致性比对.数据中心')
    }
  ]);
  const { allSchemas } = useCrudSchemas(crudSchemas);
  //列表定义
  const queryData = reactive<IQueryData>({
    queryParam: { businessSystemId: props.businessSystemId },
    pageNum: 1,
    pageSize: 10
  });
  const searchParams = toRef(queryData, 'queryParam');
  const { tableRegister, tableState, tableMethods } = useTable({
    fetchDataApi: async () => {
      const { currentPage, pageSize } = tableState;
      queryData.pageNum = unref(currentPage);
      queryData.pageSize = unref(pageSize);
      const res = await getPendingEquipmentlistApi(queryData);
      return {
        list: res.data.list,
        total: res.data.total
      };
    }
  });
  const { loading, dataList, total, currentPage, pageSize } = tableState;
  const { getList, refresh, getElTableExpose } = tableMethods;

  const batchCount = ref<number>(0);
  let elTableExpose = ref<ComponentRef<typeof ElTable>>();

  //查询
  const setSearchParams = (params: any) => {
    searchParams.value = { ...params, businessSystemId: props.businessSystemId };
    getList();
  };

  onMounted(async () => {
    elTableExpose.value = await getElTableExpose();
    watch(
      () => elTableExpose.value?.getSelectionRows(),
      (val) => {
        batchCount.value = val.length;
      },
      { deep: true }
    );
  });

  const emit = defineEmits(['checkEquipment']);
  const submit = () => {
    const selectedRows = elTableExpose.value?.getSelectionRows();
    if (selectedRows?.length == 0) {
      ElMessage.warning(t('一致性比对.请选择设备'));
      return;
    }
    emit('checkEquipment', selectedRows);
  };

  defineExpose({ submit });
</script>

<template>
  <Search :schema="allSchemas.searchSchema" @search="setSearchParams" @reset="setSearchParams" />
  <Table
    v-model:pageSize="pageSize"
    v-model:currentPage="currentPage"
    :columns="allSchemas.tableColumns"
    :data="dataList"
    :loading="loading"
    :pagination="{ total: total }"
    @register="tableRegister"
    @refresh="refresh"
    showAction
    :reserve-selection="true"
    :batch-count="batchCount"
    row-key="computerId"
    :active-u-i-d="'SystemConfigurationCheckEquipment'"
    :emptyText="t('common.暂无数据')"
  />
</template>
