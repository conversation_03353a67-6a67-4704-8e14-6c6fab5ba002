<script setup lang="ts">
  import { computed, onMounted, provide } from 'vue';
  import { propTypes } from '@/utils/propTypes';
  import { ComponentSize, ElConfigProvider } from 'element-plus';
  import { useLocaleStore } from '@/store/modules/locale';

  import { useDesign } from '@/hooks/web/useDesign';
  import { useAppStore } from '@/store/modules/app';

  const { variables } = useDesign();

  const props = defineProps({
    size: propTypes
      .oneOf<ComponentSize[]>(['default', 'small', 'large'] as ComponentSize[])
      .def('default')
  });

  provide('configGlobal', props);

  // 多语言相关
  const localeStore = useLocaleStore();

  const currentLocale = computed(() => localeStore.currentLocale);
  // 初始化所有主题色
  const appStore = useAppStore();
  onMounted(() => {
    appStore.setCssVarTheme();
  });
</script>

<template>
  <ElConfigProvider
    :namespace="variables.elNamespace"
    :locale="currentLocale.elLocale"
    :message="{ max: 1 }"
    :size="size"
  >
    <slot></slot>
  </ElConfigProvider>
</template>
