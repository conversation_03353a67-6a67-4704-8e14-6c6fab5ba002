{"extends": "@vue/tsconfig/tsconfig.web.json", "include": ["src/**/*", "src/**/*.vue", "types/**/*.d.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "lib": ["esnext", "dom"], "types": ["vite/client", "element-plus/global"], "typeRoots": ["./node_modules/@types/", "./types"], "preserveValueImports": false, "suppressImplicitAnyIndexErrors": true, "noUnusedLocals": true, "noUnusedParameters": true, "moduleResolution": "node", "noImplicitAny": false, "strictFunctionTypes": false}}