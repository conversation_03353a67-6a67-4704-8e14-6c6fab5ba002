import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router';

//菜单路由写这里
const routes: RouteRecordRaw[] = [
  {
    path: '/system-configuration',
    meta: {
      title: '系统配置'
    },
    component: () => import('@/views/SystemConfiguration/SystemConfiguration.vue')
  },
  {
    path: '/comparison-settings',
    meta: {
      title: '比对设置'
    },
    component: () => import('@/views/ComparisonSettings/ComparisonSettings.vue')
  },
  {
    path: '/programme-management',
    meta: {
      title: '方案管理'
    },
    component: () => import('@/views/ProgrammeManagement/ProgrammeManagement.vue')
  },
  {
    path: '/task-management',
    meta: {
      title: '任务管理'
    },
    component: () => import('@/views/TaskManagement/TaskManagement.vue')
  },
  {
    path: '/comparison-results',
    meta: {
      title: '比对结果'
    },
    component: () => import('@/views/ComparisonResults/ComparisonResults.vue')
  }
];
//页面跳转路由写这里
const routeLinks: RouteRecordRaw[] = [];
//404路由
const errRoutes: RouteRecordRaw[] = [
  {
    path: '/404',
    component: () => import('@/views/Error/index.vue'),
    name: 'NoFind'
  },
  {
    path: '/:path(.*)*',
    redirect: '/404',
    name: '404Page'
  }
];

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_PATH),
  routes: [...routes, ...routeLinks, ...errRoutes]
});

// 全局前置守卫
router.beforeEach((to, _from, next) => {
  if (to.path === '/404') {
    // @ts-ignore
    window.$wujie?.bus.$emit('to404Page');
  } else {
    next();
  }
});
export default router;
