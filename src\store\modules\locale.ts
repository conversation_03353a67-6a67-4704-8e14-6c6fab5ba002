import { defineStore } from 'pinia';
import { store } from '@/store';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import en from 'element-plus/es/locale/lang/en';
import { useCache } from '@/hooks/web/useCache';
const { wsCache } = useCache();

const elLocaleMap = {
  'zh-CN': zhCn,
  en: en
};
interface LocaleState {
  currentLocale: LocaleDropdownType;
  localeMap: LocaleDropdownType[];
}

export const useLocaleStore = defineStore('locales', {
  state: (): LocaleState => {
    return {
      currentLocale: {
        lang: wsCache.get('lang') || 'zh-CN',
        elLocale: elLocaleMap[wsCache.get('lang') || 'zh-CN']
      },
      // 多语言
      localeMap: [
        {
          lang: 'zh-CN',
          name: '简体中文'
        },
        {
          lang: 'en',
          name: 'English'
        }
      ]
    };
  },
  getters: {
    getCurrentLocale(): LocaleDropdownType {
      return this.currentLocale;
    },
    getLocaleMap(): LocaleDropdownType[] {
      return this.localeMap;
    }
  },
  actions: {
    setCurrentLocale(localeMap: LocaleDropdownType) {
      this.currentLocale.lang = localeMap?.lang;
      this.currentLocale.elLocale = elLocaleMap[localeMap?.lang];
      wsCache.set('lang', localeMap?.lang);
    }
  }
});

export const useLocaleStoreWithOut = () => {
  return useLocaleStore(store);
};
