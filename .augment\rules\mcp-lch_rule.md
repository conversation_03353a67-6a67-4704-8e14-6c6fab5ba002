---
type: "agent_requested"
description: "Example description"
---
//获取任务启动时创建的原子性计数值
RAtomicLong counter = redissonClient.getAtomicLong(taskFlag+"-total");
//总计数器，总数减一
long nowCount = counter.decrementAndGet();
//根据taskId获取redis-hash数据
RMap<String, String> map = redissonClient.getMap(taskFlag);
String total = map.get("total");
String eachNum = map.get("eachNum");
// 可能有多台机器监听到了mq的不同内容，这些机器同时走到了这里，就可能出现agent总数出现负数的情况
// 但是计数是原子性的，只有一个线程会出现计数结果为0，结果为0时删除redis存储的任务数据，负数的时候不需要做任何处理
if(0 == nowCount){
    //回更的agent是本次任务的最后一个agent，删除redis存储的数据值
    bucket.delete();
    bucketCount.delete();
    //任务完成推送itsm
    itsmScriptTaskResultPush.pushMessage(taskRuntimeId,"【finish】任务执行完毕",false);
    //任务完成发送统计短信
    scheduledTaskNumericalResultPush.pushMessage(taskRuntimeId);
    return;
}
//获取hash中的dto数据（驱动类型、taskId等）
TaskStartDto taskStartDto = JSON.parseObject(map.get(Constants.TASK_START_DTO), TaskStartDto.class);
if(!ObjectUtils.notEqual(taskStartDto.getDriveMode(),null)){
    taskStartDto.setDriveMode(Integer.valueOf(map.get("driveMode")));
}
//（agent总数 - 当前剩余未执行的agent）除以 并发数，取余如果是0，说明本批次执行完了，应该拉起下一批
// 如果结果不为0，说明本批次没有执行结束，直接返回
// 只适用于分批、忽略异常分批模式，队列不需要判断
if(taskStartDto.getDriveMode() != 4){
    if(0 != ((Integer.parseInt(total) - nowCount) % Integer.parseInt(eachNum))){
        return;
    }
}