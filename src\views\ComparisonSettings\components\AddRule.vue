<script setup lang="tsx">
  import { useI18n } from '@/hooks/web/useI18n';
  import { ref, reactive, watch, computed } from 'vue';
  import {
    ElForm,
    ElFormItem,
    ElSelect,
    ElOption,
    ElInput,
    FormInstance,
    ElRadioGroup,
    ElRadio
  } from 'element-plus';
  import { getCharactersetApi } from '@/api/comparisonSettings';
  import config from '@/config/axios/config';
  import { IRuleForm } from '@/api/comparisonSettings/types';
  const { t } = useI18n();
  const { result_code } = config;

  const props = defineProps({
    currentServer: {
      type: Object,
      default: () => ({})
    },
    currentRow: {
      type: Object,
      default: () => ({})
    },
    type: {
      type: String,
      default: 'add'
    }
  });
  console.log(props.currentServer);
  // 获取字符集列表
  const getCharactersetData = async () => {
    const res: any = await getCharactersetApi();
    if (res?.code === result_code) {
      charactersetList.value = res?.data;
    }
  };
  const serverInfo = ref(props.currentServer);
  const formRef = ref<FormInstance>();
  const ruleForm = reactive<IRuleForm>({
    model: 0,
    type: 1,
    encode: 'UTF-8',
    sourcePath: '',
    path: '',
    way: 0,
    childLevel: 0,
    ruleType: 0,
    content: ''
  });
  const validatePath = (_rule: any, value: any, callback: any) => {
    // 目录规则
    if (ruleForm.type === 0 && value) {
      let winReg = /^[a-zA-Z]:(((\\(?!\.|\s)[^/<>|:*?"\\]+)+\\)|(\\))$/g;
      let lnxReg = /^(\/(?!\s)[^/<>|:*?"]+)+\/$/g;
      if (!winReg.test(value) && !lnxReg.test(value)) {
        return callback(new Error(t('一致性比对.格式不正确')));
      }
      return callback();
    }
    // 文件规则
    if (ruleForm.type === 1 && value) {
      let winFileReg = /^[a-zA-Z]:(((\\(?!\.|\s)[^/<>|:*?"\\]+)+\\?)|(\\)?)$/;
      let lnxFileReg = /^(\/(?!\s)[^/<>|:*?"]+)+$/;

      if (!winFileReg.test(value) && !lnxFileReg.test(value)) {
        return callback(new Error(t('一致性比对.格式不正确')));
      }
      // 不能以空格或点结尾
      if (value.endsWith(' ') || value.endsWith('.')) {
        return callback(new Error(t('一致性比对.不能以空格或点结尾')));
      }
      return callback();
    }
    // 脚本
    if (ruleForm.type === 2 && value) {
      const scriptRegex =
        /^(?:(?:[a-zA-Z]:[\\])|(?:\/))?(?:[^\\\/:*?"<>|\x00-\x1F]+[\\\/])*file_diff[^\\\/]+$/i;
      if (!scriptRegex.test(value)) {
        return callback(new Error(t('一致性比对.格式不正确')));
      }
      // 不能以空格或点结尾
      if (value.endsWith(' ') || value.endsWith('.')) {
        return callback(new Error(t('一致性比对.脚本路径不能以空格或点结尾')));
      }
      return callback();
    }
    callback();
  };
  const rules = reactive({
    model: [{ required: true, message: t('一致性比对.请选择模式'), trigger: 'change' }],
    type: [{ required: true, message: t('一致性比对.请选择类型'), trigger: 'change' }],
    encode: [{ required: true, message: t('一致性比对.请选择字符集'), trigger: 'change' }],
    sourcePath: [
      { required: true, message: t('一致性比对.请输入源路径'), trigger: 'blur' },
      { validator: validatePath, trigger: 'blur' }
    ],
    path: [
      { required: true, message: t('一致性比对.请输入目标路径') },
      { validator: validatePath, trigger: 'blur' }
    ],
    way: [{ required: true, message: t('一致性比对.请选择模式'), trigger: 'change' }],
    content: [{ required: true, message: t('一致性比对.请输入内容') }]
  });
  const charactersetList = ref<any[]>([]);
  const sourcePathPlaceholder = computed(() => {
    if (ruleForm.type === 0) {
      return 'windows(格式为 D:\\dir\\）,linux(格式为/opt/)';
    }
    if (ruleForm.type === 1) {
      return 'windows(格式为 D:\\file）,linux(格式为/opt/file)';
    }
    if (ruleForm.type === 2) {
      return '文件名必须以file_diff开头';
    }
    return '请输入路径';
  });

  // 源路径赋值目标路径
  const onBlurSource = async () => {
    let valid = await formRef.value?.validateField('sourcePath').catch(() => {});
    if (valid) {
      ruleForm.path = !ruleForm.path ? ruleForm.sourcePath : ruleForm.path;
    }
  };

  const emit = defineEmits(['saveRule']);
  const submit = async () => {
    let valid = await formRef.value?.validate().catch(() => {});
    if (valid) {
      let params: any = {
        ...ruleForm
      };
      if (params.way === 0) {
        delete params.childLevel;
        delete params.ruleType;
        delete params.content;
      }
      emit('saveRule', params);
    }
  };

  getCharactersetData();

  defineExpose({ submit });

  watch(
    () => props.currentRow,
    (val) => {
      if (Object.keys(val).length > 0) {
        Object.assign(ruleForm, val);
      }
    },
    { deep: true, immediate: true }
  );
</script>

<template>
  <el-form
    ref="formRef"
    :model="ruleForm"
    :rules="rules"
    :inline="false"
    label-width="auto"
    :disabled="type === 'detail'"
    @submit.prevent
  >
    <div class="flex mb-3" style="color: #606266">
      <span class="mr-3">源服务器：{{ serverInfo.sourceComputerIp }}</span>
      <span>目标服务器：{{ serverInfo.targetComputerIp }}</span>
    </div>
    <el-form-item :label="t('一致性比对.模式')" prop="model" style="width: 100%">
      <el-select
        v-model="ruleForm.model"
        style="width: 100%"
        :placeholder="t('一致性比对.请选择模式')"
        disabled
        @change="ruleForm.type = ''"
      >
        <el-option :value="0" :label="t('一致性比对.比对')" />
        <el-option :value="1" :label="t('一致性比对.同步')" />
        <el-option :value="2" :label="t('一致性比对.比对后同步')" />
      </el-select>
    </el-form-item>
    <el-form-item :label="t('一致性比对.类型')" prop="type" style="width: 100%">
      <el-select
        v-model="ruleForm.type"
        style="width: 100%"
        :placeholder="t('一致性比对.请选择类型')"
      >
        <el-option :value="0" :label="t('一致性比对.目录')" />
        <el-option :value="1" :label="t('一致性比对.文件')" />
        <el-option :value="2" :label="t('一致性比对.脚本')" v-show="ruleForm.model === 0" />
      </el-select>
    </el-form-item>
    <el-form-item :label="t('一致性比对.字符集')" prop="encode" style="width: 100%">
      <el-select
        v-model="ruleForm.encode"
        filterable
        style="width: 100%"
        :placeholder="t('一致性比对.请选择字符集')"
      >
        <el-option
          :label="item.name"
          :value="item.code"
          v-for="item in charactersetList"
          :key="item.code"
        />
      </el-select>
    </el-form-item>
    <el-form-item :label="t('一致性比对.源路径')" prop="sourcePath">
      <el-input
        v-model="ruleForm.sourcePath"
        :placeholder="sourcePathPlaceholder"
        @blur="onBlurSource"
      />
    </el-form-item>
    <el-form-item :label="t('一致性比对.目标路径')" prop="path">
      <el-input v-model="ruleForm.path" :placeholder="sourcePathPlaceholder" />
    </el-form-item>
    <el-form-item :label="t('一致性比对.方式')" prop="way">
      <el-radio-group v-model="ruleForm.way">
        <el-radio :value="0">{{ t('一致性比对.全部') }}</el-radio>
        <el-radio :value="1">{{ t('一致性比对.部分') }}</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item :label="t('一致性比对.子级')" prop="childLevel" v-if="ruleForm.way === 1">
      <el-radio-group v-model="ruleForm.childLevel">
        <el-radio :value="0">{{ t('common.yes') }}</el-radio>
        <el-radio :value="1">{{ t('common.no') }}</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item :label="t('一致性比对.规则类型')" prop="ruleType" v-if="ruleForm.way === 1">
      <el-radio-group v-model="ruleForm.ruleType">
        <el-radio :value="0">{{ t('一致性比对.匹配') }}</el-radio>
        <el-radio :value="1">{{ t('一致性比对.排除') }}</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item :label="t('一致性比对.内容')" prop="content" v-if="ruleForm.way === 1">
      <el-input
        v-model="ruleForm.content"
        type="textarea"
        :autosize="{ minRows: 3 }"
        :placeholder="t('一致性比对.请输入内容')"
      />
    </el-form-item>
  </el-form>
</template>
