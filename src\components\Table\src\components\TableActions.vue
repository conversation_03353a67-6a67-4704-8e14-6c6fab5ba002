<script lang="tsx">
  import { defineComponent, PropType, watch, ref, nextTick } from 'vue';
  import {
    ElTooltip,
    ElPopover,
    ElScrollbar,
    ElTable,
    ElCheckboxGroup,
    ElCheckbox,
    ElButton
  } from 'element-plus';
  import Draggable from 'vuedraggable';
  import { Icon } from '@/components/Icon';
  import { useI18n } from '@/hooks/web/useI18n';
  import type { TableColumn } from '@/components/Table/src/types';
  import { cloneDeep } from 'lodash-es';
  import { useRouter } from 'vue-router';
  import { propTypes } from '@/utils/propTypes';
  import { useCache } from '@/hooks/web/useCache';
  const { wsCache } = useCache();

  const { t } = useI18n();

  const DEFAULT_FILTER_COLUMN = ['index', 'selection', 'action', 'expand'];
  export default defineComponent({
    name: 'TableActions',
    props: {
      columns: {
        type: Array as PropType<TableColumn[]>,
        default: () => []
      },
      elTableRef: {
        type: Object as PropType<ComponentRef<typeof ElTable>>,
        default: () => {}
      },
      // 表格工具栏缓存唯一标识符
      activeUID: propTypes.string.def('')
    },
    setup(props) {
      const elTableRef = ref(props.elTableRef);
      const tableColumns = ref(props.columns);
      let oldColumns = cloneDeep(tableColumns);
      const { currentRoute } = useRouter();
      const fullPath = currentRoute.value.path;
      const activeUID = ref(props.activeUID);
      const cacheTableHeadersKey = `${fullPath}_${activeUID.value}`;
      const checkAll = ref(false);
      const isIndeterminate = ref(true); // 如果为True，则表示为半选状态
      const handleCheckAllChange = (val: boolean) => {
        tableColumns.value.forEach((item) => {
          if (DEFAULT_FILTER_COLUMN.includes(item.field)) {
            return;
          }
          if (item.disabled !== true) {
            item.hidden = !val;
            item.show = val;
          }
        });
        isIndeterminate.value = false;
      };

      const handleCheckChange = async (value?: any) => {
        if (value) {
          tableColumns.value.map((item) => {
            if (DEFAULT_FILTER_COLUMN.includes(item.field)) {
              return;
            }
            item.show = value.includes(item.field);
            item.hidden = !item.show;
          });
        }
        checkAll.value = tableColumns.value.every((item) => {
          if (DEFAULT_FILTER_COLUMN.includes(item.field)) {
            return true;
          }
          return item.show;
        });
        if (checkAll.value) {
          isIndeterminate.value = false;
        } else {
          isIndeterminate.value = tableColumns.value.some((item) => item.show);
        }
      };
      const resetTableColumns = async () => {
        Object.assign(tableColumns.value, cloneDeep(oldColumns.value));
        await nextTick();
        wsCache.delete(cacheTableHeadersKey);
        await handleCheckChange();
      };
      let showField = ref(wsCache.get(cacheTableHeadersKey));
      if (cacheTableHeadersKey) {
        tableColumns.value.forEach((item) => {
          item.show = !item.hidden;
          item.hidden = !item.show;
        });
        oldColumns = cloneDeep(tableColumns);
        if (showField.value) {
          tableColumns.value.sort((a: TableColumn, b: TableColumn) => {
            if (
              DEFAULT_FILTER_COLUMN.includes(a.field) ||
              showField.value.indexOf(a.field) === -1 ||
              showField.value.indexOf(b.field) === -1
            ) {
              return 0;
            }
            const indexA = showField.value.indexOf(a.field);
            const indexB = showField.value.indexOf(b.field);
            return indexA - indexB;
          });
          tableColumns.value.forEach((item) => {
            if (DEFAULT_FILTER_COLUMN.includes(item.field)) return;
            item.hidden = !showField.value.includes(item.field);
            item.show = showField.value.includes(item.field);
          });
        }
        handleCheckChange();
      }

      watch(
        () => tableColumns.value,
        async (newColumns) => {
          showField.value = newColumns
            .filter((item) => item.show && !DEFAULT_FILTER_COLUMN.includes(item.field))
            .map((item) => item.field);
          wsCache.set(cacheTableHeadersKey, showField.value);
          elTableRef.value?.doLayout();
        },
        {
          deep: true,
          immediate: true
        }
      );

      return () => (
        <>
          <div class="absolute right-3 top-3 z-10">
            <ElTooltip content={t('common.columnSetting')} placement="top">
              <span>
                <ElPopover trigger="click" width="auto" placement="bottom-end" class="w-[100%]">
                  {{
                    default: () => {
                      return (
                        <div>
                          <div class="flex justify-between border-bottom-1 pb-1">
                            <ElCheckbox
                              onChange={handleCheckAllChange}
                              indeterminate={isIndeterminate.value}
                              v-model={checkAll.value}
                            >
                              {t('common.selectAll')}
                            </ElCheckbox>
                            <ElButton onClick={resetTableColumns}>{t('common.reset')}</ElButton>
                          </div>
                          <ElScrollbar max-height="400px">
                            <ElCheckboxGroup v-model={showField.value} onChange={handleCheckChange}>
                              <Draggable
                                list={tableColumns.value}
                                item-key="field"
                                v-slots={{
                                  item: ({ element }) => (
                                    <div v-show={!DEFAULT_FILTER_COLUMN.includes(element.field)}>
                                      <span class="cursor-move mr-10px">
                                        <Icon icon="carbon:drag-vertical" />
                                      </span>
                                      <ElCheckbox
                                        value={element.field}
                                        disabled={DEFAULT_FILTER_COLUMN.includes(element.field)}
                                        label={element.label}
                                        key={element.field}
                                      />
                                    </div>
                                  )
                                }}
                              />
                            </ElCheckboxGroup>
                          </ElScrollbar>
                        </div>
                      );
                    },
                    reference: () => {
                      return (
                        <span class="h-full flex items-center">
                          <Icon
                            icon="ph:grid-four-fill"
                            class="cursor-pointer"
                            color="var(--top-header-text-color)"
                            hoverColor="var(--el-color-primary)"
                          />
                        </span>
                      );
                    }
                  }}
                </ElPopover>
              </span>
            </ElTooltip>
          </div>
        </>
      );
    }
  });
</script>
