<script setup lang="tsx">
  import { useI18n } from '@/hooks/web/useI18n';
  import { Table } from '@/components/Table';
  import { useTable } from '@/hooks/web/useTable';
  import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas';
  import { ref, reactive, unref, watch, onMounted } from 'vue';
  import type { BatchOption } from '@/components/Table/src/types';
  import { ElTable, ElButton, ElDrawer, ElMessage } from 'element-plus';
  import {
    getEquipmentlistApi,
    saveEquipmentApi,
    removeComputerApi
  } from '@/api/systemConfiguration';
  import ButtonGroup from '@/components/ButtonGroup/src/ButtonGroup.vue';
  import type { ButtonGroupSchema } from '@/components/ButtonGroup/src/types';
  import type { IQueryData, IEquipment } from '@/api/systemConfiguration/types';
  import config from '@/config/axios/config';
  import CheckEquipment from './CheckEquipment.vue';

  const { result_code } = config;
  const { t } = useI18n();

  const props = defineProps({
    currentRowSystem: {
      type: Object,
      default: () => ({})
    }
  });

  const crudSchemas = reactive<CrudSchema[]>([
    {
      field: 'selection', //复选框
      table: {
        type: 'selection'
      }
    },
    {
      field: 'index',
      label: t('demo.index'),
      type: 'index'
    },
    {
      field: 'computerName',
      label: t('一致性比对.设备名称')
    },
    {
      field: 'computerIp',
      label: t('一致性比对.设备IP')
    },
    {
      field: 'centerName',
      label: t('一致性比对.数据中心')
    }
  ]);
  const { allSchemas } = useCrudSchemas(crudSchemas);
  //列表定义
  const selectedItemIds = ref<number[]>([]);
  const queryData = reactive<IQueryData>({
    queryParam: {},
    pageNum: 1,
    pageSize: 10
  });

  const { tableRegister, tableState, tableMethods } = useTable({
    immediate: false,
    fetchDataApi: async () => {
      const { currentPage, pageSize } = tableState;
      queryData.pageNum = unref(currentPage);
      queryData.pageSize = unref(pageSize);
      const res = await getEquipmentlistApi(queryData);
      return {
        list: res.data.list,
        total: res.data.total
      };
    },
    fetchDelApi: async () => {
      const res = await removeComputerApi(selectedItemIds.value);
      return !!res;
    }
  });
  const { loading, dataList, total, currentPage, pageSize } = tableState;
  const { getList, getElTableExpose, refresh, delList } = tableMethods;

  const actionVisible = ref(false);
  const batchCount = ref<number>(0);
  const saveLoading = ref(false);
  const checkequipmentRef = ref<InstanceType<typeof CheckEquipment>>();

  //删除
  const delLoading = ref(false);
  let elTableExpose = ref<ComponentRef<typeof ElTable>>();
  const delData = async (row: IEquipment | null) => {
    selectedItemIds.value = row
      ? [row.id]
      : elTableExpose.value!.getSelectionRows().map((v: IEquipment) => v.id) || [];
    delLoading.value = true;
    await delList(unref(selectedItemIds).length).finally(() => {
      delLoading.value = false;
    });
  };

  /*按钮组定义*/
  const buttonSchema = ref<ButtonGroupSchema[]>([
    {
      type: 'primary',
      field: 'add',
      name: t('common.add'),
      perm: 'saveComputer',
      option: () => {
        addActionFn();
      }
    }
  ]);
  /*批处理表格*/
  const batchOptions = ref<BatchOption[]>([
    {
      name: t('common.delete'),
      icon: 'mdi:trash-can-outline',
      title: t('common.delMessage'),
      perm: 'removeComputer',
      option: () => {
        delData(null);
      }
    }
  ]);

  // 新增业务系统
  const addActionFn = () => {
    if (Object.keys(props.currentRowSystem).length == 0) {
      ElMessage.warning(t('一致性比对.请选择业务系统'));
      return;
    }
    actionVisible.value = true;
  };

  /*保存表单*/
  const saveFn = async () => {
    checkequipmentRef.value!.submit(); // 调用子组件的方法
  };
  const submitCheckEquipment = async (checkDatas: any) => {
    console.log('submitCheckEquipment', checkDatas);
    const checkedDatas = checkDatas.map((item: any) => {
      return {
        computerId: item?.computerId,
        computerIp: item?.computerIp,
        computerName: item?.computerName,
        centerId: item?.centerId,
        centerName: item?.centerName,
        businessSystemId: props.currentRowSystem.businessSystemId
      };
    });
    saveLoading.value = true;
    const res: any = await saveEquipmentApi(checkedDatas)
      .catch(() => {})
      .finally(() => {
        saveLoading.value = false;
      });
    if (res && res.code == result_code) {
      ElMessage.success(t('common.addSuccess'));
      actionVisible.value = false;
      currentPage.value = 1;
      await getList();
    } else {
      ElMessage.error(res?.message);
    }
  };

  /*详情 */
  onMounted(async () => {
    elTableExpose.value = await getElTableExpose();
    watch(
      () => elTableExpose.value?.getSelectionRows(),
      (val) => {
        batchCount.value = val.length;
      },
      { deep: true }
    );
  });

  watch(
    () => props.currentRowSystem,
    (val) => {
      if (Object.keys(val).length == 0) {
        dataList.value = [];
        return;
      }
      queryData.queryParam = {
        businessSystemId: val.businessSystemId
      };
      getList();
    },
    { deep: true, immediate: true }
  );
</script>

<template>
  <ButtonGroup :schema="buttonSchema" />
  <Table
    class="mt-1"
    v-model:pageSize="pageSize"
    v-model:currentPage="currentPage"
    :columns="allSchemas.tableColumns"
    :data="dataList"
    :loading="loading"
    :pagination="{ total: total }"
    @register="tableRegister"
    @refresh="refresh"
    :batch-count="batchCount"
    :batch-options="batchOptions"
    :reserve-selection="true"
    row-key="computerId"
    :active-u-i-d="'SystemConfigurationEquipmentTable'"
    :emptyText="t('common.暂无数据')"
  />
  <ElDrawer
    v-model="actionVisible"
    :title="t('common.add') + t('一致性比对.设备')"
    destroy-on-close
    size="50%"
    class="customForm"
  >
    <CheckEquipment
      ref="checkequipmentRef"
      :business-system-id="currentRowSystem?.businessSystemId"
      @check-equipment="submitCheckEquipment"
    />
    <template #footer>
      <ElButton type="primary" :loading="saveLoading" @click="saveFn">
        {{ t('common.ok') }}
      </ElButton>
      <ElButton @click="actionVisible = false">{{ t('common.cancel') }}</ElButton>
    </template>
  </ElDrawer>
</template>
