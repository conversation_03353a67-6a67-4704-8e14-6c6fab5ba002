<script lang="tsx">
  import { defineComponent, PropType, ref, unref, watch } from 'vue';
  import { propTypes } from '@/utils/propTypes';
  import { ElButton, ElDropdown, ElDropdownItem, ElDropdownMenu, ElPopconfirm } from 'element-plus';
  import { useIcon } from '@/hooks/web/useIcon';
  import { findIndex } from '@/utils';
  import type { FormSchema } from '@/components/Form';
  import type { ButtonGroupSchema } from '@/components/ButtonGroup/src/types';
  import { hasPermi } from '@/directives';

  export default defineComponent({
    name: 'ButtonGroup',
    props: {
      // 生成Button的布局结构数组
      schema: {
        type: Array as PropType<ButtonGroupSchema[]>,
        default: () => []
      },
      // 是否显示伸缩
      showExpand: propTypes.bool.def(false),
      // 伸缩的界限字段
      expandField: propTypes.string.def('')
    },
    setup(props) {
      const schema = ref<ButtonGroupSchema[]>(props.schema);
      const visibleSchema = ref<ButtonGroupSchema[]>([]);

      // 监听结构化数组，重新生成按钮
      watch(
        () => unref(props.schema),
        (propSchema = []) => {
          schema.value = [];
          visibleSchema.value = [];
          if (!props.showExpand) {
            schema.value = propSchema;
          } else {
            const index = findIndex(propSchema, (v: FormSchema) => v.field === props.expandField);
            unref(propSchema).map((v, i) => {
              if (i >= index) {
                visibleSchema.value.push(v);
              } else {
                schema.value.push(v);
              }
            });
          }
        },
        {
          immediate: true,
          deep: true
        }
      );

      return () => (
        <>
          <div class="flex pt-1 flex-wrap">
            {schema.value.map((v) => {
              const hasPerm = v.perm ? hasPermi(v.perm) : true;
              if (!hasPerm) {
                return null;
              }
              return (
                <span v-show={unref(!v.hidden)}>
                  {v.title ? (
                    <ElPopconfirm {...v} onConfirm={() => v.option()}>
                      {{
                        reference: () => {
                          return (
                            <ElButton class="m-1 ml-0" plain {...v}>
                              {v.name}
                            </ElButton>
                          );
                        }
                      }}
                    </ElPopconfirm>
                  ) : (
                    <ElButton class="m-1 ml-0" plain {...v} onClick={() => v.option()}>
                      {v.name}
                    </ElButton>
                  )}
                </span>
              );
            })}
            {props.showExpand ? (
              <ElDropdown trigger="click">
                {{
                  default: () => {
                    return (
                      <ElButton icon={useIcon({ icon: 'ri:more-fill' })} plain class="m-1 ml-0" />
                    );
                  },
                  dropdown: () => {
                    return (
                      <ElDropdownMenu>
                        {{
                          default: () => {
                            return unref(visibleSchema)
                              .filter((v) => (v.perm ? hasPermi(v.perm) : true))
                              .map((v) => {
                                return (
                                  <ElDropdownItem v-show={unref(!v.hidden)} onClick={v.option}>
                                    {v.name}
                                  </ElDropdownItem>
                                );
                              });
                          }
                        }}
                      </ElDropdownMenu>
                    );
                  }
                }}
              </ElDropdown>
            ) : null}
          </div>
        </>
      );
    }
  });
</script>
