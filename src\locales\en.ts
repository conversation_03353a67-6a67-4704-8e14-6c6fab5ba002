export default {
  common: {
    inputText: 'Please input',
    selectText: 'Please select',
    startTimeText: 'Start time',
    endTimeText: 'End time',
    login: 'Login',
    required: 'This is required',
    loginOut: 'Login out',
    personal: 'Person',
    document: 'Document',
    reminder: 'Reminder',
    loginOutMessage: 'Exit the system？',
    ok: 'OK',
    cancel: 'Cancel',
    reload: 'Reload current',
    closeTab: 'Close current',
    closeTheLeftTab: 'Close left',
    closeTheRightTab: 'Close right',
    closeOther: 'Close other',
    closeAll: 'Close all',
    prevLabel: 'Prev',
    nextLabel: 'Next',
    skipLabel: 'Jump',
    doneLabel: 'End',
    menu: 'Menu',
    menuDes: 'Menu bar rendered in routed structure',
    collapse: 'Collapse',
    collapseDes: 'Expand and zoom the menu bar',
    tagsView: 'Tags view',
    tagsViewDes: 'Used to record routing history',
    tool: 'Tool',
    toolDes: 'Used to set up custom systems',
    query: 'Query',
    reset: 'Reset',
    shrink: 'Put away',
    expand: 'Expand',
    handle: 'handle',
    add: 'add',
    edit: 'edit',
    detail: 'detail',
    save: 'save',
    delData: 'delete data',
    delMessage: 'Delete the selected data?',
    delWarning: 'Warning',
    delNoData: 'Please select the data to delete',
    delSuccess: 'Deleted successfully',
    loginExpirationPrompt: 'Your login information has expired, please login again',
    delete: 'delete',
    import: 'import',
    export: 'export',
    excel: 'Please select the excel type file',
    upload: 'upload',
    selectFile: 'select file',
    selectAll: 'all',
    yes: 'yes',
    no: 'no',
    refresh: 'refresh',
    fullscreen: 'fullscreen',
    size: 'size',
    columnSetting: 'columnSetting',
    selected: 'selected：'
  },
  error: {
    noPermission: `Sorry, you don't have permission to access this page.`,
    pageError: 'Sorry, the page you visited does not exist.',
    networkError: 'Sorry, the server reported an error.',
    returnToHome: 'Return to home'
  },
  size: {
    default: 'Default',
    large: 'Large',
    small: 'Small'
  },
  demo: {
    index: 'index',
    ifullName: 'fullName',
    iemail: 'email',
    itelephone: 'telephone',
    iidnumber: 'idnumber',
    org: 'org',
    post: 'post',
    ipassword: 'password',
    ipwUpdateTime: 'passwordUpdateTime',
    ilocked: 'isLocked',
    icenterName: 'centerName',
    action: 'action',
    addOrg: 'addOrg',
    editOrg: 'editOrg',
    delOrg: 'delOrg',
    orgName: 'orgName',
    selected: 'selected：',
    jumpToLR: 'jumpToLR',
    jumpToTab: 'jumpToTab',
    popConfirm: 'popConfirm',
    popConfirmTitle: 'popConfirmTitle？'
  }
};
