<script lang="tsx">
  import { defineComponent, PropType, ref, unref, watch } from 'vue';
  import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>confirm, ElText } from 'element-plus';
  import { propTypes } from '@/utils/propTypes';
  import { useIcon } from '@/hooks/web/useIcon';
  import { useI18n } from '@/hooks/web/useI18n';
  import { hasPermi } from '@/directives';

  const { t } = useI18n();

  export default defineComponent({
    name: 'TableBatch',
    props: {
      //是否有批处理
      batchCount: propTypes.number.def(0),
      //批处理操作
      batchOptions: {
        type: Array as PropType<any>,
        default: () => []
      }
    },
    setup(props) {
      const batchCount = ref(props.batchCount);
      const visible = ref(false);
      watch(
        () => props.batchCount,
        () => {
          batchCount.value = props.batchCount;
          visible.value = batchCount.value > 0;
        },
        {
          immediate: true,
          deep: true
        }
      );
      const batchOptions = ref(props.batchOptions);

      return () => (
        <>
          <div
            v-model={visible.value}
            class="text-right h-35px flex items-center justify-between bg-blue-100 bg-opacity-50 px-4 my-1"
          >
            <div v-model={batchCount.value} class="h-full flex items-center">
              <ElText className="mr-4">
                {t('common.selected')} {batchCount.value}
              </ElText>
              {batchOptions.value.map((v) => {
                const hasPerm = v.perm ? hasPermi(v.perm) : true;
                if (!hasPerm) {
                  return null;
                }
                return (
                  <div v-show={unref(!v.hidden)}>
                    {v.title ? (
                      <ElPopconfirm {...v} onConfirm={() => v.option()}>
                        {{
                          reference: () => {
                            return (
                              <ElButton
                                class="m-1"
                                link
                                type="primary"
                                icon={useIcon({ icon: v.icon })}
                              >
                                {v.name}
                              </ElButton>
                            );
                          }
                        }}
                      </ElPopconfirm>
                    ) : (
                      <ElButton
                        class="m-1"
                        link
                        type="primary"
                        icon={useIcon({ icon: v.icon })}
                        onClick={() => v.option()}
                      >
                        {v.name}
                      </ElButton>
                    )}
                    <ElDivider direction="vertical" />
                  </div>
                );
              })}
            </div>
          </div>
        </>
      );
    }
  });
</script>
