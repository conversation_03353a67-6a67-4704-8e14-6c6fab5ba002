<script setup lang="tsx">
  import { useI18n } from '@/hooks/web/useI18n';
  import { Search } from '@/components/Search';
  import { Table } from '@/components/Table';
  import { useTable } from '@/hooks/web/useTable';
  import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas';
  import { ref, reactive, unref, toRef, onMounted, nextTick } from 'vue';
  import { getBusinessSystemlistApi } from '@/api/systemConfiguration';
  import type { IQueryData, ISystemConfiguration } from '@/api/comparisonSettings/types';
  import { ElTable } from 'element-plus';

  const { t } = useI18n();

  const crudSchemas = reactive<CrudSchema[]>([
    {
      field: 'index',
      label: t('demo.index'),
      type: 'index'
    },
    {
      field: 'businessSystemName',
      label: t('一致性比对.业务系统'),
      search: {
        component: 'Input'
      }
    },
    {
      field: 'businessSystemDesc',
      label: t('一致性比对.系统描述'),
      search: {
        component: 'Input'
      }
    },
    {
      field: 'businessSystemCode',
      label: t('一致性比对.系统缩写')
    }
  ]);
  const { allSchemas } = useCrudSchemas(crudSchemas);
  //列表定义
  const queryData = reactive<IQueryData>({
    queryParam: {},
    pageNum: 1,
    pageSize: 10
  });
  const searchParams = toRef(queryData, 'queryParam');
  const { tableRegister, tableState, tableMethods } = useTable({
    fetchDataApi: async () => {
      const { currentPage, pageSize } = tableState;
      queryData.pageNum = unref(currentPage);
      queryData.pageSize = unref(pageSize);
      const res = await getBusinessSystemlistApi(queryData);
      nextTick(() => {
        if (res.data?.list.length > 0) {
          rowClickFn(res.data.list[0]);
          setCurrent(res.data.list[0]);
        } else {
          currentRow.value = {};
          setCurrent();
        }
      });
      return {
        list: res.data.list,
        total: res.data.total
      };
    }
  });
  const { loading, dataList, total, currentPage, pageSize } = tableState;
  const { getList, getElTableExpose, refresh } = tableMethods;

  let elTableExpose = ref<ComponentRef<typeof ElTable>>();
  const currentRow = ref<any>({});

  //查询
  const setSearchParams = (params: any) => {
    searchParams.value = params;
    getList();
  };

  const emit = defineEmits(['rowClick']); // 定义一个自定义事件，用于传递参数给父组件
  const rowClickFn = (row: ISystemConfiguration) => {
    currentRow.value = row;
    emit('rowClick', row); // 触发父组件的事件并传递参数
  };

  const setCurrent = (row?: any) => {
    elTableExpose.value!.setCurrentRow(row);
  };

  onMounted(async () => {
    elTableExpose.value = await getElTableExpose();
  });
</script>

<template>
  <Search :schema="allSchemas.searchSchema" @search="setSearchParams" @reset="setSearchParams" />
  <Table
    class="mt-1 Rd-system-table"
    v-model:pageSize="pageSize"
    v-model:currentPage="currentPage"
    :columns="allSchemas.tableColumns"
    :data="dataList"
    :loading="loading"
    :pagination="{ total: total }"
    @register="tableRegister"
    @refresh="refresh"
    @row-click="rowClickFn"
    highlight-current-row
    showAction
    row-key="id"
    :active-u-i-d="'ComparisonSettingsSystemTable'"
    :emptyText="t('common.暂无数据')"
  />
</template>
<style lang="less">
  .Rd-system-table {
    .el-table__body {
      .el-table__row {
        cursor: pointer;
      }
    }
  }
</style>
